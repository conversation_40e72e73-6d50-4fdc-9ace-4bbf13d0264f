<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境切换时序图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>⏱️ 环境切换时序图</h1>
        <div class="mermaid">
sequenceDiagram
    participant P as Py视频创作工具
    participant API as 工具API接口服务
    participant Mock as AI模拟服务
    participant Real as 真实AI平台

    Note over P,Mock: 本地开发阶段
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Mock: 真实调用AI平台格式接口<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Note over Mock: ✅ 唯一模拟职责<br/>1. 按对应AI平台要求验证参数<br/>2. 模拟对应平台响应状态
    alt 参数验证失败
        Mock->>API: 返回对应AI平台格式参数错误
    else 参数验证通过
        Mock->>API: 模拟成功/失败/超时状态
    end
    API->>P: 透明传递模拟结果

    Note over P,Real: 生产环境
    P->>API: 请求AI生成（图像/视频/文本/语音等）
    Note over API: 🚫 严禁模拟行为<br/>必须真实调用AI服务
    API->>Real: 真实调用AI平台接口<br/>(DeepSeek/LiblibAI/KlingAI/MiniMax/火山引擎豆包)
    Real->>API: 返回真实AI平台响应
    API->>P: 透明传递真实结果
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
