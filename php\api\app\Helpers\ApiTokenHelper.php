<?php

namespace App\Helpers;

use Illuminate\Support\Str;
use Torann\Hashids\Facade\Hashids;

class ApiTokenHelper
{
    /**
     * 生成Token（增强版，包含过期时间）
     *
     * @param int $user_id
     * @param int|null $ttl 过期时间（秒），null使用默认配置
     * @return string
     */
    public static function generateToken($user_id, $ttl = null)
    {
        $ttl = $ttl ?? (config('auth.token_ttl_days', 30) * 24 * 3600);
        $expires_at = time() + $ttl;

        $random_str = Str::random(env('TOKEN_RAND_LEN', 16));
        $hash_str = Hashids::encode([
            $user_id,
            $expires_at, // 使用过期时间而不是当前时间
            mt_rand(0, 10000)
        ]);
        return $random_str . $hash_str;
    }

    /**
     * 生成Token（兼容旧版本）
     *
     * @param int $user_id
     * @return string
     */
    public static function generateTokenLegacy($user_id)
    {
        $random_str = Str::random(env('TOKEN_RAND_LEN', 16));
        $hash_str = Hashids::encode([
            $user_id,
            time(),
            mt_rand(0, 10000)
        ]);
        return $random_str . $hash_str;
    }

    /**
     * 从Token中获取用户ID
     *
     * @param string $token
     * @return int|false
     */
    public static function getUserIdByToken($token = '')
    {
        $tokenData = self::parseToken($token);
        return $tokenData ? $tokenData['user_id'] : false;
    }

    /**
     * 解析Token获取完整信息
     *
     * @param string $token
     * @return array|false 返回 ['user_id' => int, 'expires_at' => int, 'random' => int] 或 false
     */
    public static function parseToken($token = '')
    {
        if (empty($token)) {
            return false;
        }

        $randLen = env('TOKEN_RAND_LEN', 16);
        if (strlen($token) <= $randLen) {
            return false;
        }

        $hash_str = substr($token, $randLen);
        $arr = Hashids::decode($hash_str);

        if (empty($arr) || count($arr) < 3) {
            return false;
        }

        return [
            'user_id' => $arr[0],
            'expires_at' => $arr[1],
            'random' => $arr[2]
        ];
    }

    /**
     * 验证Token是否过期
     *
     * @param string $token
     * @return bool true表示已过期
     */
    public static function isTokenExpired($token = '')
    {
        $tokenData = self::parseToken($token);
        if (!$tokenData) {
            return true; // 无法解析的Token视为过期
        }

        return time() > $tokenData['expires_at'];
    }

    /**
     * 获取Token剩余有效时间
     *
     * @param string $token
     * @return int 剩余秒数，-1表示已过期，-2表示Token无效
     */
    public static function getTokenRemainingTime($token = '')
    {
        $tokenData = self::parseToken($token);
        if (!$tokenData) {
            return -2; // Token无效
        }

        $remaining = $tokenData['expires_at'] - time();
        return $remaining > 0 ? $remaining : -1; // -1表示已过期
    }

    /**
     * @param string $token
     * @return string
     */
    public static function encryptToken($token = '')
    {
        return md5($token);
    }
}
