<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WEB网页工具9: 代理结算流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>💰 WEB网页工具9: 代理结算流程（环境切换优化版）</h1>
        <div class="mermaid">
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant TP as 第三方支付平台

    Note over W: 佣金查询
    W->>A: 查询佣金余额(Token)
    A->>A: 验证Token和代理身份
    A->>DB: 查询代理佣金记录
    A->>DB: 计算可提现佣金
    A->>W: 返回佣金详情

    Note over W: 提现申请
    W->>A: 提现申请(提现金额/收款信息/Token)
    A->>DB: 验证提现条件
    alt 条件不满足
        A->>W: 返回提现失败(条件不满足)
    else 条件满足
        A->>DB: 创建提现申请记录
        A->>DB: 冻结对应佣金金额
        A->>W: 返回申请提交成功

        Note over A: 系统自动或人工审核
        A->>DB: 更新审核状态
        alt 审核不通过
            A->>DB: 解冻佣金金额
            A->>W: 通知审核不通过
        else 审核通过
            A->>TP: 调用转账接口
            TP->>A: 返回转账结果
            A->>DB: 更新提现状态
            A->>DB: 记录佣金支出
            A->>W: 通知提现成功
        end
    end
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>