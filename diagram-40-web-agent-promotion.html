<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WEB网页工具8: 代理推广流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>🚀 WEB网页工具8: 代理推广流程（环境切换优化版）</h1>
        <div class="mermaid">
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    Note over W: 代理申请流程
    W->>A: 代理申请请求(个人信息/Token)
    A->>A: 验证Token有效性
    A->>DB: 检查是否已是代理
    alt 已是代理
        A->>W: 返回已是代理状态
    else 非代理用户
        A->>DB: 创建代理申请记录
        A->>W: 返回申请提交成功(待审核)
    end

    Note over W: 推广链接生成
    W->>A: 生成推广链接请求(Token)
    A->>DB: 验证代理身份
    A->>A: 生成唯一推广码
    A->>DB: 保存推广链接记录
    A->>W: 返回推广链接

    Note over W: 推广数据统计
    W->>A: 查询推广统计(时间范围/Token)
    A->>R: 查询缓存统计数据
    alt 缓存命中
        R->>A: 返回统计数据
    else 缓存未命中
        A->>DB: 查询推广点击数据
        A->>DB: 查询注册转化数据
        A->>DB: 查询充值转化数据
        A->>A: 计算推广效果指标
        A->>R: 缓存统计结果
    end
    A->>W: 返回推广统计报告
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>