<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境切换架构图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>🌐 环境切换架构图</h1>
        <div class="mermaid">
graph TB
    subgraph "本地开发环境"
        A[Py视频创作工具] --> B[工具API接口服务]
        B --> E[AI服务集成模拟返回数据服务]
        E -.->|仅模拟，不真实调用| F1[DeepSeek API格式模拟<br/>剧情生成/角色生成]
        E -.->|仅模拟，不真实调用| F2[LiblibAI API格式模拟<br/>图像生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F3[KlingAI API格式模拟<br/>图像生成/视频生成/角色生成/风格生成]
        E -.->|仅模拟，不真实调用| F4[MiniMax API格式模拟<br/>全业务支持]
        E -.->|仅模拟，不真实调用| F5[火山引擎豆包 API格式模拟<br/>语音合成/音效生成/音色生成]

        B --> T[第三方服务集成模拟返回数据服务]
        T -.->|仅模拟，不真实调用| G1[微信服务API格式模拟<br/>OAuth登录/微信支付]
        T -.->|仅模拟，不真实调用| G2[支付宝API格式模拟<br/>统一收单/退款查询]
        T -.->|仅模拟，不真实调用| G3[短信服务API格式模拟<br/>阿里云/腾讯云短信]
        T -.->|仅模拟，不真实调用| G4[邮件服务API格式模拟<br/>SMTP/SendCloud]
    end

    subgraph "生产环境"
        A2[Py视频创作工具] --> B2[工具API接口服务]
        B2 --> F6[真实第三方AI平台<br/>DeepSeek/LiblibAI/KlingAI<br/>MiniMax/火山引擎豆包]
        B2 --> G5[真实第三方服务平台<br/>微信/支付宝/阿里云/腾讯云]
    end

    style E fill:#fce4ec,stroke:#e91e63
    style T fill:#fff8e1,stroke:#ff9800
    style F1 fill:#ffebee
    style F2 fill:#ffebee
    style F3 fill:#ffebee
    style F4 fill:#ffebee
    style F5 fill:#ffebee
    style G1 fill:#ffebee
    style G2 fill:#ffebee
    style G3 fill:#ffebee
    style G4 fill:#ffebee
    style F6 fill:#e8f5e8,stroke:#4caf50
    style G5 fill:#e8f5e8,stroke:#4caf50
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
