<?php

namespace App\Exceptions;

use Exception;

class TokenException extends Exception
{
    protected $errorCode;
    protected $errorData;

    public function __construct($message = "", $errorCode = 0, $errorData = [], Exception $previous = null)
    {
        parent::__construct($message, 0, $previous);
        $this->errorCode = $errorCode;
        $this->errorData = $errorData;
    }

    /**
     * 获取错误码
     */
    public function getErrorCode()
    {
        return $this->errorCode;
    }

    /**
     * 获取错误数据
     */
    public function getErrorData()
    {
        return $this->errorData;
    }

    /**
     * 转换为API响应格式
     */
    public function toResponse()
    {
        return [
            'code' => $this->errorCode,
            'message' => $this->getMessage(),
            'data' => $this->errorData
        ];
    }

    // 静态工厂方法
    public static function invalidFormat($token = '')
    {
        return new self(
            config('auth.error_messages.token_invalid_format', 'Token格式无效'),
            config('auth.error_codes.token_invalid_format', 4001),
            ['token_hint' => substr($token, 0, 8) . '...']
        );
    }

    public static function blacklisted($reason = 'unknown')
    {
        return new self(
            config('auth.error_messages.token_blacklisted', 'Token已失效'),
            config('auth.error_codes.token_blacklisted', 4002),
            ['reason' => $reason]
        );
    }

    public static function expired($expiresAt = null)
    {
        return new self(
            config('auth.error_messages.token_expired', 'Token已过期'),
            config('auth.error_codes.token_expired', 4003),
            ['expires_at' => $expiresAt ? date('Y-m-d H:i:s', $expiresAt) : null]
        );
    }

    public static function notFound()
    {
        return new self(
            config('auth.error_messages.token_not_found', 'Token不存在'),
            config('auth.error_codes.token_not_found', 4004)
        );
    }

    public static function userDisabled($userId = null)
    {
        return new self(
            config('auth.error_messages.user_disabled', '账户已被禁用'),
            config('auth.error_codes.user_disabled', 4005),
            ['user_id' => $userId]
        );
    }

    public static function userNotFound($userId = null)
    {
        return new self(
            config('auth.error_messages.user_not_found', '用户不存在'),
            config('auth.error_codes.user_not_found', 4006),
            ['user_id' => $userId]
        );
    }

    public static function revoked($reason = 'manual')
    {
        return new self(
            config('auth.error_messages.token_revoked', 'Token已被撤销'),
            config('auth.error_codes.token_revoked', 4007),
            ['reason' => $reason]
        );
    }

    public static function deviceLimitExceeded($maxDevices = null)
    {
        return new self(
            config('auth.error_messages.device_limit_exceeded', '设备数量超出限制'),
            config('auth.error_codes.device_limit_exceeded', 4008),
            ['max_devices' => $maxDevices]
        );
    }
}
