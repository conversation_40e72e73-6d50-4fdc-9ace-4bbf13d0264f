<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Py视频创作工具 - 项目创建流程图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .diagram-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .mermaid {
            text-align: center;
        }
        
        .description {
            background: #e3f2fd;
            border-left: 5px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .description h3 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .feature-card h4 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        .feature-card ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .feature-card li {
            margin: 8px 0;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 Py视频创作工具</h1>
            <p>项目创建流程图 - C-0</p>
        </div>
        
        <div class="content">
            <div class="description">
                <h3>📋 流程概述</h3>
                <p>本流程图展示了用户在Py视频创作工具中创建新的视频创作项目的完整过程，包括用户认证、项目信息收集、数据库记录创建、文件系统初始化等关键步骤。</p>
            </div>
            
            <div class="diagram-container">
                <div class="mermaid">
sequenceDiagram
    participant U as 用户
    participant F as 前端界面
    participant A as 工具API接口服务
    participant Auth as 认证服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant FS as 文件存储服务

    Note over U: 🎬 用户开始创建视频创作项目

    %% 用户认证检查
    U->>F: 访问视频创作页面
    F->>Auth: 检查用户登录状态
    Auth->>F: 返回用户信息/要求登录
    
    alt 用户未登录
        F->>U: 跳转到登录页面
        U->>Auth: 用户登录
        Auth->>DB: 验证用户凭据
        Auth->>F: 返回认证Token
    end

    Note over F: 📝 项目创建表单

    %% 项目信息收集
    F->>U: 显示项目创建表单
    U->>F: 填写项目信息<br/>- 项目名称: "小猫咪的森林冒险"<br/>- 项目描述: "儿童友好的冒险故事"<br/>- 项目类型: "video"<br/>- 目标受众: "儿童(3-8岁)"<br/>- 风格偏好: "卡通可爱"

    %% 模板选择（可选）
    F->>A: GET /py-api/projects/templates?type=video
    A->>DB: 查询视频项目模板
    DB->>A: 返回模板列表
    A->>F: 返回可用模板
    F->>U: 显示模板选择（可选）
    U->>F: 选择模板或选择空白项目

    Note over F: 🚀 创建项目记录

    %% 提交项目创建请求
    F->>A: POST /py-api/projects/create<br/>项目信息 + 用户Token
    A->>Auth: 验证用户Token
    Auth->>A: 返回用户ID和权限

    %% 检查用户权限和配额
    A->>DB: 检查用户项目配额
    A->>DB: 检查用户订阅状态
    
    alt 配额不足或权限不够
        A->>F: 返回错误信息
        F->>U: 显示升级提示
    else 权限验证通过
        %% 创建项目记录
        A->>DB: INSERT INTO p_projects<br/>(user_id, title, description, type, status, config)
        DB->>A: 返回项目ID
        
        %% 创建项目文件夹结构
        A->>FS: 创建项目文件夹<br/>/projects/{project_id}/
        FS->>A: 确认文件夹创建
        
        %% 初始化项目配置
        A->>DB: INSERT INTO p_project_configs<br/>初始化项目配置
        A->>R: 缓存项目基础信息
        
        %% 创建默认的工作区
        A->>DB: INSERT INTO p_project_modules<br/>创建四个模块工作区<br/>- story_module (故事创作)<br/>- character_module (角色选择)<br/>- image_module (图片生成)<br/>- video_module (视频编辑)
        
        A->>F: 返回项目创建成功<br/>project_id + 项目信息
    end

    Note over F: 🎯 项目初始化完成

    %% 跳转到项目工作台
    F->>U: 显示项目创建成功
    F->>A: GET /py-api/projects/{project_id}/workspace
    A->>DB: 查询项目详细信息
    A->>F: 返回项目工作台数据
    F->>U: 跳转到项目工作台<br/>准备开始创作流程

    Note over U: ✅ 项目创建完成，进入AI任务调度流程
                </div>
            </div>
            
            <div class="features">
                <div class="feature-card">
                    <h4>🔐 用户认证</h4>
                    <ul>
                        <li>自动检查登录状态</li>
                        <li>Token验证机制</li>
                        <li>权限和配额检查</li>
                        <li>订阅状态验证</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>📝 项目信息</h4>
                    <ul>
                        <li>项目名称和描述</li>
                        <li>项目类型选择</li>
                        <li>目标受众设定</li>
                        <li>风格偏好配置</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>🗄️ 数据存储</h4>
                    <ul>
                        <li>p_projects表记录</li>
                        <li>项目配置初始化</li>
                        <li>模块工作区创建</li>
                        <li>Redis缓存优化</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h4>📁 文件系统</h4>
                    <ul>
                        <li>项目文件夹创建</li>
                        <li>模块目录结构</li>
                        <li>资源存储准备</li>
                        <li>工作台初始化</li>
                    </ul>
                </div>
            </div>
            
            <div class="description">
                <h3>🔄 后续流程</h3>
                <p>项目创建完成后，用户将进入项目工作台，开始具体的创作流程：</p>
                <ul>
                    <li><strong>C-1: AI任务调度流程</strong> - 智能平台选择和任务分发</li>
                    <li><strong>故事创作模块</strong> - AI辅助故事生成和编辑</li>
                    <li><strong>角色选择模块</strong> - 角色库浏览和自定义</li>
                    <li><strong>图片生成模块</strong> - AI图片生成和编辑</li>
                    <li><strong>视频编辑模块</strong> - 视频制作和后期处理</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });
    </script>
</body>
</html>
