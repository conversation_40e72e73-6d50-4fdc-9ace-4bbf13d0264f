<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Py视频创作工具业务流程C-0: 视频创作项目创建流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>🎬 Py视频创作工具业务流程C-0: 视频创作项目创建流程（纯文本数据处理版）</h1>
        <div class="mermaid">
sequenceDiagram
    participant U as 用户
    participant F as Py视频创作工具前端
    participant A as 工具API接口服务
    participant C1 as WebSocket服务
    participant AI as 文生文AI服务
    participant DB as MySQL数据库
    participant R as Redis缓存

    Note over A: ⚠️ 重要：本流程仅处理文本数据，严禁储存或中转任何资源文件

    Note over U: 🎬 用户进入项目创建页面

    %% Token验证流程（参考diagram-22）
    U->>F: 访问视频创作页面
    F->>A: API请求(携带Token)
    A->>A: 解析JWT Token
    alt Token格式无效
        A->>F: 返回认证失败(Token无效)
        F->>U: 跳转到登录页面
    else Token格式有效
        A->>R: 检查Token是否在黑名单
        alt Token在黑名单
            A->>F: 返回认证失败(Token已失效)
            F->>U: 跳转到登录页面
        else Token有效
            A->>A: 验证Token签名和过期时间
            alt Token过期或签名无效
                A->>F: 返回认证失败(Token过期)
                F->>U: 跳转到登录页面
            else Token验证通过
                A->>DB: 查询用户当前状态
                alt 用户被禁用
                    A->>R: 将Token加入黑名单
                    A->>F: 返回认证失败(账户被禁用)
                    F->>U: 跳转到登录页面
                else 用户状态正常
                    Note over A: Token验证通过，继续项目创建流程
                end
            end
        end
    end

    %% 用户登录流程（参考diagram-21）
    alt 需要登录
        U->>A: 用户登录请求(用户名/密码)
        A->>DB: 查询用户信息
        alt 用户不存在
            A->>F: 返回登录失败(用户不存在)
        else 用户存在
            A->>A: 验证密码正确性
            alt 密码错误
                A->>DB: 记录登录失败次数
                A->>F: 返回登录失败(密码错误)
            else 密码正确
                A->>DB: 检查用户状态
                alt 用户被禁用
                    A->>F: 返回登录失败(账户被禁用)
                else 用户状态正常
                    A->>A: 生成JWT Token
                    A->>R: 存储Token(24小时过期)
                    A->>DB: 更新最后登录时间
                    A->>DB: 清除登录失败次数
                    A->>F: 返回登录成功(Token+用户信息)
                end
            end
        end
    end

    Note over F: 🎨 项目创建界面布局

    %% 加载风格图片列表
    F->>A: GET /py-api/projects/style-templates
    A->>DB: 查询风格图片模板
    DB->>A: 返回风格图片列表
    A->>F: 返回风格选项
    F->>U: 显示左侧风格图片列表<br/>+ 右侧内容输入区域

    Note over U: 👤 用户选择风格和内容类型

    %% 用户选择风格
    U->>F: 选择风格图片<br/>(卡通可爱/写实风格/科幻风格等)
    F->>F: 更新右侧区域显示<br/>显示内容类型选择

    %% 用户选择内容类型并输入
    U->>F: 选择内容类型 + 输入内容

    alt 选择"自有故事剧情分镜"
        U->>F: 在多行输入框录入自有故事<br/>"小猫咪在森林中寻找宝藏的冒险故事..."
        Note over F: 📝 自有故事分镜处理
    else 选择"AI故事分镜"
        U->>F: 在多行输入框录入提示词/故事大纲<br/>"创作一个关于小猫咪冒险的儿童故事"
        Note over F: 🤖 AI故事生成+分镜处理
    end

    Note over F: 🚀 用户点击提交按钮

    %% 用户体验优化：提交时的界面反馈
    U->>F: 点击提交按钮
    F->>F: 界面保持不变 + 显示处理罩层<br/>显示进度条和状态文字<br/>禁用提交按钮，防止重复提交

    %% WebSocket连接建立
    F->>C1: 建立WebSocket连接<br/>订阅项目处理进度频道

    %% 提交项目创建请求
    F->>A: POST /py-api/projects/create-with-story<br/>风格选择 + 内容类型 + 故事内容 + Token
    A->>Auth: 验证用户Token和权限
    A->>DB: 检查用户项目配额和订阅状态

    alt 权限验证失败
        A->>F: 返回错误信息
        F->>F: 隐藏处理罩层
        F->>U: 显示升级提示
    else 权限验证通过
        %% 创建项目记录
        A->>DB: INSERT INTO p_projects<br/>(user_id, style_id, content_type, status='processing')
        DB->>A: 返回项目ID

        Note over A: 🤖 AI处理阶段（实时进度推送）

        %% 进度推送：开始处理
        A->>C1: 推送进度更新<br/>progress: 10%, status: "开始AI处理"
        C1->>F: WebSocket推送进度
        F->>U: 更新进度条：10% "开始AI处理"

        %% AI处理分支
        alt 自有故事剧情分镜
            A->>C1: 推送进度更新<br/>progress: 30%, status: "分析故事结构"
            C1->>F: WebSocket推送进度
            F->>U: 更新进度条：30% "分析故事结构"

            A->>AI: 调用文生文AI进行分镜处理<br/>prompt: "对以下故事进行分镜，提取标题，不修改原故事"<br/>story: 用户输入的故事

            A->>C1: 推送进度更新<br/>progress: 60%, status: "生成分镜脚本"
            C1->>F: WebSocket推送进度
            F->>U: 更新进度条：60% "生成分镜脚本"

            AI->>A: 返回分镜结果 + 提取的项目标题

            A->>C1: 推送进度更新<br/>progress: 80%, status: "提取项目标题"
            C1->>F: WebSocket推送进度
            F->>U: 更新进度条：80% "提取项目标题"

        else AI故事分镜
            A->>C1: 推送进度更新<br/>progress: 20%, status: "分析故事大纲"
            C1->>F: WebSocket推送进度
            F->>U: 更新进度条：20% "分析故事大纲"

            A->>AI: 调用文生文AI生成故事+分镜<br/>prompt: "根据以下大纲生成完整故事并分镜"<br/>outline: 用户输入的提示词/大纲

            A->>C1: 推送进度更新<br/>progress: 50%, status: "生成完整故事"
            C1->>F: WebSocket推送进度
            F->>U: 更新进度条：50% "生成完整故事"

            A->>C1: 推送进度更新<br/>progress: 70%, status: "创建分镜脚本"
            C1->>F: WebSocket推送进度
            F->>U: 更新进度条：70% "创建分镜脚本"

            AI->>A: 返回生成的故事 + 分镜结果 + 项目标题

            A->>C1: 推送进度更新<br/>progress: 85%, status: "优化分镜内容"
            C1->>F: WebSocket推送进度
            F->>U: 更新进度条：85% "优化分镜内容"
        end

        %% 保存处理结果
        A->>C1: 推送进度更新<br/>progress: 90%, status: "保存项目数据"
        C1->>F: WebSocket推送进度
        F->>U: 更新进度条：90% "保存项目数据"

        A->>DB: UPDATE p_projects SET<br/>title=AI提取的标题, story_content=故事内容,<br/>storyboard=分镜结果, status='created'
        A->>FS: 创建项目文件夹<br/>/projects/{project_id}/
        A->>DB: 初始化项目配置和模块工作区
        A->>R: 缓存项目信息

        A->>C1: 推送进度更新<br/>progress: 100%, status: "项目创建完成"
        C1->>F: WebSocket推送进度
        F->>U: 更新进度条：100% "项目创建完成"

        A->>F: 返回项目创建成功<br/>project_id + 项目标题 + 故事内容 + 分镜数据
    end

    Note over F: 🎨 界面渲染优化

    %% 用户体验优化：处理完成后的界面更新
    F->>C1: 关闭WebSocket连接<br/>取消订阅进度频道
    F->>F: 隐藏处理罩层和进度条
    F->>F: 在原多行输入框位置渲染剧情分镜列表<br/>每个分镜显示：场景描述、时长、操作按钮
    F->>F: 激活"下一步：绑角色"按钮<br/>按钮变为彩色，表示满足进入下一步条件

    Note over F: ✏️ 分镜编辑功能

    F->>U: 显示可编辑的分镜列表<br/>支持操作：<br/>- 手动编辑分镜内容<br/>- 合并相邻分镜<br/>- 拖拽调整顺序<br/>- 删除不需要的分镜<br/>- 添加新分镜

    Note over U: ✅ 项目创建完成，可进入下一步或继续编辑分镜
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
