<?php

/**
 * 全面分析现有数据库表结构
 * 通过模型文件和数据库信息获取完整的表结构
 */

// 模型文件路径
$modelPath = 'php/api/app/Models/';

// 获取所有模型文件
$modelFiles = glob($modelPath . '*.php');

echo "📊 现有数据库表结构全面分析\n";
echo "=" . str_repeat("=", 80) . "\n\n";

$existingTables = [];

// 分析每个模型文件
foreach ($modelFiles as $file) {
    $fileName = basename($file, '.php');
    $content = file_get_contents($file);
    
    // 提取表名
    if (preg_match('/protected\s+\$table\s*=\s*[\'"]([^\'"]+)[\'"]/', $content, $matches)) {
        $tableName = $matches[1];
        $actualTableName = 'p_' . $tableName; // 添加前缀
        
        echo "🔍 分析模型: $fileName\n";
        echo "📋 表名: $actualTableName (模型中定义: $tableName)\n";
        
        // 提取fillable字段
        $fillableFields = [];
        if (preg_match('/protected\s+\$fillable\s*=\s*\[(.*?)\]/s', $content, $fillableMatches)) {
            $fillableContent = $fillableMatches[1];
            preg_match_all('/[\'"]([^\'"]+)[\'"]/', $fillableContent, $fieldMatches);
            $fillableFields = $fieldMatches[1];
        }
        
        // 提取关联关系
        $relationships = [];
        if (preg_match_all('/public\s+function\s+(\w+)\(\).*?return\s+\$this->(hasMany|belongsTo|hasOne|belongsToMany)\(([^)]+)\)/s', $content, $relMatches)) {
            for ($i = 0; $i < count($relMatches[0]); $i++) {
                $relationships[] = [
                    'method' => $relMatches[1][$i],
                    'type' => $relMatches[2][$i],
                    'target' => $relMatches[3][$i]
                ];
            }
        }
        
        // 提取常量定义
        $constants = [];
        if (preg_match_all('/const\s+(\w+)\s*=\s*[\'"]([^\'"]+)[\'"]/', $content, $constMatches)) {
            for ($i = 0; $i < count($constMatches[0]); $i++) {
                $constants[$constMatches[1][$i]] = $constMatches[2][$i];
            }
        }
        
        // 提取casts定义
        $casts = [];
        if (preg_match('/protected\s+\$casts\s*=\s*\[(.*?)\]/s', $content, $castsMatches)) {
            $castsContent = $castsMatches[1];
            preg_match_all('/[\'"]([^\'"]+)[\'"]\s*=>\s*[\'"]([^\'"]+)[\'"]/', $castsContent, $castFieldMatches);
            for ($i = 0; $i < count($castFieldMatches[0]); $i++) {
                $casts[$castFieldMatches[1][$i]] = $castFieldMatches[2][$i];
            }
        }
        
        $existingTables[$actualTableName] = [
            'model_name' => $fileName,
            'table_name' => $actualTableName,
            'model_table_name' => $tableName,
            'fillable_fields' => $fillableFields,
            'relationships' => $relationships,
            'constants' => $constants,
            'casts' => $casts,
            'file_path' => $file
        ];
        
        echo "📝 可填充字段 (" . count($fillableFields) . "个): " . implode(', ', $fillableFields) . "\n";
        echo "🔗 关联关系 (" . count($relationships) . "个): ";
        foreach ($relationships as $rel) {
            echo "{$rel['method']}({$rel['type']}) ";
        }
        echo "\n";
        
        if (!empty($constants)) {
            echo "📌 常量定义: ";
            foreach ($constants as $name => $value) {
                echo "$name=$value ";
            }
            echo "\n";
        }
        
        echo "\n" . str_repeat("-", 60) . "\n\n";
    }
}

echo "📋 现有表总结 (" . count($existingTables) . "个表):\n";
echo "=" . str_repeat("=", 80) . "\n";

foreach ($existingTables as $tableName => $info) {
    echo sprintf("%-30s | %-20s | %2d字段 | %2d关联\n", 
        $tableName, 
        $info['model_name'], 
        count($info['fillable_fields']), 
        count($info['relationships'])
    );
}

// 按业务功能分类
echo "\n📊 按业务功能分类:\n";
echo "=" . str_repeat("=", 80) . "\n";

$categories = [
    '用户管理' => ['p_users'],
    '项目管理' => ['p_projects'],
    'AI生成' => ['p_ai_generation_tasks', 'p_ai_model_configs'],
    '资源管理' => ['p_resources', 'p_resource_versions', 'p_resource_downloads', 'p_resource_exports'],
    '内容库' => ['p_style_library', 'p_character_library', 'p_character_categories'],
    '积分系统' => ['p_points_transactions', 'p_points_freeze'],
    '社交功能' => ['p_user_works', 'p_work_plaza', 'p_work_shares', 'p_work_interactions', 'p_follows', 'p_likes', 'p_comments'],
    '系统管理' => ['p_websocket_sessions', 'p_platform_performance_metrics', 'p_platform_usage_statistics', 'p_user_model_preferences']
];

foreach ($categories as $category => $tables) {
    echo "\n🔹 $category:\n";
    foreach ($tables as $table) {
        if (isset($existingTables[$table])) {
            $info = $existingTables[$table];
            echo "  ✅ $table ({$info['model_name']}) - " . count($info['fillable_fields']) . "个字段\n";
        } else {
            echo "  ❌ $table - 不存在\n";
        }
    }
}

// 保存为JSON文件
$jsonData = [
    'analysis_time' => date('Y-m-d H:i:s'),
    'total_tables' => count($existingTables),
    'tables' => $existingTables,
    'categories' => $categories
];

file_put_contents('existing_tables_analysis.json', json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo "\n✅ 分析完成，详细信息已保存到: existing_tables_analysis.json\n";
