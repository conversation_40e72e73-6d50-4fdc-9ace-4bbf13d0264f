<?php
require_once 'php/api/vendor/autoload.php';

// 加载环境变量
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    // 数据库连接配置
    $host = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $port = $_ENV['DB_PORT'] ?? 3306;
    $database = $_ENV['DB_DATABASE'] ?? 'ai_tool';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? 'root';
    $prefix = $_ENV['DB_PREFIX'] ?? 'p_';
    
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "✅ 数据库连接成功\n";
    echo "📋 数据库: $database (表前缀: $prefix)\n\n";
    
    // 获取所有表
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📊 数据库中的所有表 (" . count($tables) . " 个):\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    foreach ($tables as $table) {
        // 获取表的行数
        $countStmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
        $count = $countStmt->fetch()['count'];
        
        // 获取表注释
        $commentStmt = $pdo->query("SELECT TABLE_COMMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = '$database' AND TABLE_NAME = '$table'");
        $comment = $commentStmt->fetch()['TABLE_COMMENT'] ?? '';
        
        echo sprintf("%-30s | %8s 行 | %s\n", $table, number_format($count), $comment);
    }
    
    echo "\n" . str_repeat("=", 60) . "\n";
    
    // 重点分析用户相关表
    $userTables = array_filter($tables, function($table) use ($prefix) {
        return strpos($table, $prefix . 'user') === 0 || 
               strpos($table, $prefix . 'point') === 0 ||
               strpos($table, $prefix . 'work') === 0 ||
               strpos($table, $prefix . 'agent') === 0 ||
               strpos($table, $prefix . 'ai_') === 0;
    });
    
    if (!empty($userTables)) {
        echo "\n🔍 重点表结构分析:\n";
        echo "=" . str_repeat("=", 80) . "\n";
        
        foreach ($userTables as $table) {
            echo "\n📋 表: $table\n";
            echo "-" . str_repeat("-", 50) . "\n";
            
            $stmt = $pdo->query("DESCRIBE `$table`");
            $columns = $stmt->fetchAll();
            
            foreach ($columns as $column) {
                $field = $column['Field'];
                $type = $column['Type'];
                $null = $column['Null'] === 'YES' ? 'NULL' : 'NOT NULL';
                $default = $column['Default'] !== null ? "默认: {$column['Default']}" : '';
                $extra = $column['Extra'] ? "({$column['Extra']})" : '';
                
                echo sprintf("  %-25s | %-20s | %-8s | %s %s\n", 
                    $field, $type, $null, $default, $extra);
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    exit(1);
}
