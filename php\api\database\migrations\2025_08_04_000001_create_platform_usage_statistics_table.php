<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建平台使用统计表
 * 
 * 🚨 升级：支持用户平台选择和偏好记录
 * 用于记录用户对各AI平台的使用统计、成功率、满意度等数据
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('platform_usage_statistics', function (Blueprint $table) {
            $table->id();
            
            // 用户关联
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            // 任务和平台信息
            $table->string('task_type', 50)->comment('任务类型：image_generation, video_generation等');
            $table->string('platform', 50)->comment('AI平台：liblib, kling, minimax等');
            
            // 使用统计
            $table->unsignedInteger('usage_count')->default(0)->comment('总使用次数');
            $table->unsignedInteger('success_count')->default(0)->comment('成功次数');
            $table->unsignedInteger('failure_count')->default(0)->comment('失败次数');
            
            // 成本统计
            $table->decimal('total_cost', 10, 4)->default(0)->comment('总消费积分');
            
            // 性能统计
            $table->decimal('average_response_time', 8, 2)->default(0)->comment('平均响应时间（秒）');
            
            // 用户满意度
            $table->decimal('user_satisfaction_score', 3, 2)->default(0)->comment('用户满意度评分（1-5）');
            $table->unsignedInteger('user_rating_count')->default(0)->comment('用户评分次数');
            
            // JSON字段
            $table->json('performance_metrics')->nullable()->comment('性能指标详情');
            $table->json('error_details')->nullable()->comment('错误详情统计');
            
            // 时间戳
            $table->timestamp('last_used_at')->nullable()->comment('最后使用时间');
            $table->timestamps();
            
            // 索引
            $table->unique(['user_id', 'task_type', 'platform'], 'unique_user_task_platform');
            $table->index(['task_type', 'platform'], 'idx_task_platform');
            $table->index(['user_id', 'last_used_at'], 'idx_user_last_used');
            $table->index(['platform', 'usage_count'], 'idx_platform_usage');
            $table->index(['task_type', 'user_satisfaction_score'], 'idx_task_satisfaction');
            
            $table->comment('平台使用统计表 - 记录用户对各AI平台的使用数据和偏好');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('platform_usage_statistics');
    }
};
