<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Token Configuration
    |--------------------------------------------------------------------------
    |
    | Token相关配置选项
    |
    */

    // Token滑动刷新配置
    'token_sliding_refresh' => env('TOKEN_SLIDING_REFRESH', true),
    
    // 每次滑动刷新延长的天数
    'sliding_days' => env('TOKEN_SLIDING_DAYS', 30),
    
    // 最大滑动时间（天）
    'max_sliding_days' => env('TOKEN_MAX_SLIDING_DAYS', 180),
    
    // Token默认过期时间（天）
    'token_ttl_days' => env('TOKEN_TTL_DAYS', 30),
    
    // Refresh Token过期时间（天）
    'refresh_token_ttl_days' => env('REFRESH_TOKEN_TTL_DAYS', 60),
    
    // 黑名单Token过期时间（天）
    'blacklist_ttl_days' => env('BLACKLIST_TTL_DAYS', 30),
    
    // Token使用日志保留天数
    'usage_log_retention_days' => env('TOKEN_USAGE_LOG_RETENTION_DAYS', 7),
    
    // 是否启用Token使用日志
    'enable_usage_logging' => env('TOKEN_ENABLE_USAGE_LOGGING', true),
    
    // 多设备Token管理
    'max_devices_per_user' => env('MAX_DEVICES_PER_USER', 5),
    
    // 是否启用设备管理
    'enable_device_management' => env('ENABLE_DEVICE_MANAGEMENT', false),
    
    /*
    |--------------------------------------------------------------------------
    | Error Codes
    |--------------------------------------------------------------------------
    |
    | Token验证相关错误码定义
    |
    */
    'error_codes' => [
        'token_invalid_format' => 4001,
        'token_blacklisted' => 4002,
        'token_expired' => 4003,
        'token_not_found' => 4004,
        'user_disabled' => 4005,
        'user_not_found' => 4006,
        'token_revoked' => 4007,
        'device_limit_exceeded' => 4008,
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Error Messages
    |--------------------------------------------------------------------------
    |
    | 错误消息定义（支持国际化）
    |
    */
    'error_messages' => [
        'token_invalid_format' => 'Token格式无效',
        'token_blacklisted' => 'Token已失效',
        'token_expired' => 'Token已过期',
        'token_not_found' => 'Token不存在',
        'user_disabled' => '账户已被禁用',
        'user_not_found' => '用户不存在',
        'token_revoked' => 'Token已被撤销',
        'device_limit_exceeded' => '设备数量超出限制',
    ],
];
