<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Py视频创作工具业务流程C-1: AI任务调度流程（用户选择平台+环境切换版）</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>🤖 Py视频创作工具业务流程C-1: AI任务调度流程（用户选择平台+环境切换版）</h1>
        <div class="mermaid">
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant AiModel as AiModelController
    participant SC as AiServiceClient
    participant Mock as 虚拟AI平台
    participant DeepSeek as DeepSeek API
    participant LiblibAI as LiblibAI API
    participant KlingAI as KlingAI API
    participant MiniMax as MiniMax API
    participant Doubao as 火山引擎豆包 API
    participant DB as MySQL数据库
    participant R as Redis缓存

    Note over P: 🎬 视频创作工具启动平台选择流程

    %% 接口131: 获取平台选项
    P->>AiModel: GET /py-api/ai-models/platform-options<br/>?task_type=video_generation
    AiModel->>SC: 调用getPlatformOptions(video_generation)
    SC->>SC: 查询支持视频生成的平台配置
    SC->>AiModel: 返回平台列表[KlingAI, LiblibAI, MiniMax]<br/>含质量评分、速度、成本、特性
    AiModel->>P: 返回完整平台选项数据

    %% 接口132: 获取推荐平台
    P->>AiModel: GET /py-api/ai-models/user-recommendations<br/>?task_type=video_generation&limit=3
    AiModel->>SC: 调用getUserRecommendations(userId, video_generation, 3)
    SC->>DB: 查询用户历史偏好和使用记录
    SC->>SC: 基于用户偏好算法排序平台
    SC->>AiModel: 返回个性化推荐[KlingAI(推荐), LiblibAI, MiniMax]<br/>含推荐理由
    AiModel->>P: 返回推荐平台列表

    Note over P: 🎯 用户选择模式
    alt 用户选择"智能推荐"
        %% 接口125: 选择最优平台
        P->>AiModel: POST /py-api/ai-models/select-platform<br/>business_type=video, criteria={priority:'quality'}
        AiModel->>SC: 调用智能平台选择服务
        SC->>SC: 分析用户偏好+当前平台状态
        SC->>AiModel: 返回最优平台选择结果
        AiModel->>P: 返回选中平台+选择理由
    else 用户手动选择平台
        P->>P: 用户从平台列表中选择KlingAI
    end

    Note over P: 📝 提交视频生成任务
    P->>A: 提交AI任务(video_generation/参数/选择的平台/Token)
    A->>A: 验证Token和任务参数
    A->>DB: 检查用户积分和权限
    A->>DB: 创建AI任务记录(含用户选择的平台+选择方式)

    A->>SC: 调用AiServiceClient(指定平台)

    Note over SC: 🚨 环境切换机制
    alt 开发环境 (AI_SERVICE_MODE=mock)
        SC->>Mock: 调用虚拟AI平台<br/>模拟用户选择的平台响应
        Mock->>SC: 返回模拟结果(快速响应)

    else 生产环境 (AI_SERVICE_MODE=real)
        alt 用户选择DeepSeek
            SC->>DeepSeek: 调用DeepSeek API
            DeepSeek->>SC: 返回生成结果
        else 用户选择LiblibAI
            SC->>LiblibAI: 调用LiblibAI API
            LiblibAI->>SC: 返回生成结果
        else 用户选择KlingAI
            SC->>KlingAI: 调用KlingAI API
            KlingAI->>SC: 返回生成结果
        else 用户选择MiniMax
            SC->>MiniMax: 调用MiniMax API
            MiniMax->>SC: 返回生成结果
        else 用户选择火山引擎豆包
            SC->>Doubao: 调用火山引擎豆包 API
            Doubao->>SC: 返回生成结果
        end
    end

    SC->>A: 返回AI生成结果(含实际使用的平台信息)
    A->>DB: 更新任务状态、结果和平台使用记录
    A->>R: 缓存任务结果
    A->>P: 返回AI生成结果

    Note over A: 📊 用户偏好学习与优化
    A->>DB: 记录用户平台选择行为<br/>(手动选择/智能推荐/选择理由)
    A->>DB: 更新用户偏好权重<br/>(质量优先/速度优先/成本优先)
    A->>R: 更新用户常用平台缓存
    A->>R: 刷新推荐算法缓存<br/>为下次推荐优化准备数据
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
