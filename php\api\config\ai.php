<?php

return [
    /*
    |--------------------------------------------------------------------------
    | AI服务配置
    |--------------------------------------------------------------------------
    |
    | 🚨 架构边界规范：环境切换机制
    | ✅ 本地开发：工具API → AI模拟服务 → 模拟响应（无真实调用）
    | ✅ 生产环境：工具API → 真实AI平台 → 真实响应（真实调用）
    |
    | 环境切换通过 AI_SERVICE_MODE 环境变量控制：
    | - mock: 调用模拟服务（开发环境）
    | - real: 调用真实AI平台（生产环境）
    |
    */

    // 🚨 环境切换配置
    'service_mode' => env('AI_SERVICE_MODE', 'mock'), // mock | real

    // 模拟服务配置（开发环境）
    'mock_service' => [
        'base_url' => env('AI_MOCK_URL', 'https://aiapi.tiptop.cn'),
        'timeout' => env('AI_MOCK_TIMEOUT', 30),
        'enabled' => true,
    ],

    // 真实服务配置（生产环境）
    'real_service' => [
        'timeout' => env('AI_REAL_TIMEOUT', 60),
        'enabled' => true,
    ],
    
    // 🚨 AI平台配置（支持环境切换）
    'platforms' => [
        'deepseek' => [
            'name' => 'DeepSeek',
            'description' => '剧情生成和分镜脚本专家',
            'supports' => ['text_generation', 'story_generation', 'character_generation'],
            'mock_endpoint' => '/deepseek/chat/completions',
            'real_api' => [
                'base_url' => env('DEEPSEEK_API_URL', 'https://api.deepseek.com'),
                'api_key' => env('DEEPSEEK_API_KEY', ''),
                'endpoint' => '/chat/completions',
            ],
            'model' => 'deepseek-chat',
            'timeout' => 30,
        ],
        'liblib' => [
            'name' => 'LiblibAI',
            'description' => '图像生成专业平台',
            'supports' => ['image_generation'],
            'mock_endpoint' => '/api/open/xingliu/text2img',
            'real_api' => [
                'base_url' => env('LIBLIB_API_URL', 'https://openapi.liblibai.cloud'),
                'api_key' => env('LIBLIB_API_KEY', ''),
                'endpoint' => '/api/open/xingliu/text2img',
            ],
            'model' => 'star-3-alpha',
            'timeout' => 60,
        ],
        'kling' => [
            'name' => 'KlingAI',
            'description' => '视频生成领导者',
            'supports' => ['image_generation', 'video_generation'],
            'mock_endpoint' => '/kling/v1/images/generations',
            'real_api' => [
                'base_url' => env('KLING_API_URL', 'https://api.klingai.com'),
                'api_key' => env('KLING_API_KEY', ''),
                'endpoint' => '/v1/images/generations',
            ],
            'model' => 'kling-v2-master',
            'timeout' => 180,
        ],
        'minimax' => [
            'name' => 'MiniMax',
            'description' => '多模态AI平台',
            'supports' => ['text_generation', 'image_generation', 'voice_synthesis', 'video_generation', 'music_generation'],
            'mock_endpoint' => '/minimax/v1/text/chatcompletion_v2',
            'real_api' => [
                'base_url' => env('MINIMAX_API_URL', 'https://api.minimaxi.chat'),
                'api_key' => env('MINIMAX_API_KEY', ''),
                'group_id' => env('MINIMAX_GROUP_ID', ''),
                'endpoint' => '/v1/text/chatcompletion_v2',
            ],
            'model' => 'speech-01',
            'timeout' => 60,
        ],
        'volcengine' => [
            'name' => '火山引擎豆包',
            'description' => '专业语音AI平台',
            'supports' => ['voice_synthesis', 'sound_generation', 'music_generation'],
            'mock_endpoint' => '/volcengine/bigmodel/voices/synthesize',
            'real_api' => [
                'base_url' => env('VOLCENGINE_API_URL', 'https://openspeech.bytedance.com'),
                'api_key' => env('VOLCENGINE_API_KEY', ''),
                'app_id' => env('VOLCENGINE_APP_ID', ''),
                'endpoint' => '/api/v1/tts',
            ],
            'model' => 'doubao-voice',
            'timeout' => 60,
        ],
    ],

    // 默认平台配置
    'default_platforms' => [
        'text_generation' => 'deepseek',
        'story_generation' => 'deepseek',
        'character_generation' => 'deepseek',
        'image_generation' => 'liblib',
        'video_generation' => 'kling',
        'voice_synthesis' => 'minimax',
        'sound_generation' => 'volcengine',
        'music_generation' => 'minimax',
    ],

    // 生成参数默认值
    'generation_defaults' => [
        'text' => [
            'temperature' => 0.8,
            'max_tokens' => 1000,
            'top_p' => 0.9,
        ],
        'image' => [
            'aspect_ratio' => '16:9',
            'quality' => 'standard',
            'style' => 'realistic',
        ],
        'story' => [
            'length' => 'medium',
            'temperature' => 0.8,
            'max_tokens' => 2000,
        ],
        'character' => [
            'temperature' => 0.8,
            'max_tokens' => 1000,
        ],
    ],

    // 成本配置
    'cost_settings' => [
        'base_cost_per_token' => 0.0001,
        'platform_multipliers' => [
            'deepseek' => 1.0,
            'liblib' => 2.0,
            'kling' => 2.5,
            'minimax' => 1.5,
            'volcengine' => 1.8,
        ],
        'quality_multipliers' => [
            'standard' => 1.0,
            'hd' => 2.0,
            'ultra' => 3.0,
        ],
    ],

    // 重试配置
    'retry' => [
        'max_attempts' => 3,
        'delay_seconds' => 2,
        'backoff_multiplier' => 2,
    ],

    // 缓存配置
    'cache' => [
        'enabled' => env('AI_CACHE_ENABLED', true),
        'ttl' => env('AI_CACHE_TTL', 3600), // 1小时
        'prefix' => 'ai_generation:',
    ],

    /*
    |--------------------------------------------------------------------------
    | 🚨 环境切换辅助函数
    |--------------------------------------------------------------------------
    */

    /**
     * 获取当前服务模式
     */
    'get_service_mode' => function() {
        return config('ai.service_mode', 'mock');
    },

    /**
     * 判断是否为模拟模式
     */
    'is_mock_mode' => function() {
        return config('ai.service_mode', 'mock') === 'mock';
    },

    /**
     * 获取平台API配置
     */
    'get_platform_config' => function($platform) {
        $config = config("ai.platforms.{$platform}");
        if (!$config) {
            return null;
        }

        $serviceMode = config('ai.service_mode', 'mock');

        if ($serviceMode === 'mock') {
            // 模拟模式：使用模拟服务
            return [
                'base_url' => config('ai.mock_service.base_url'),
                'endpoint' => $config['mock_endpoint'],
                'timeout' => config('ai.mock_service.timeout'),
                'mode' => 'mock'
            ];
        } else {
            // 真实模式：使用真实API
            return [
                'base_url' => $config['real_api']['base_url'],
                'endpoint' => $config['real_api']['endpoint'],
                'api_key' => $config['real_api']['api_key'],
                'timeout' => config('ai.real_service.timeout'),
                'mode' => 'real'
            ];
        }
    },

    /**
     * 获取第三方服务配置
     */
    'third_party_config' => [
        'service_mode' => env('THIRD_PARTY_MODE', 'mock'), // mock | real
        'mock_service' => [
            'base_url' => env('THIRD_PARTY_MOCK_URL', 'https://thirdapi.tiptop.cn'),
            'timeout' => env('THIRD_PARTY_MOCK_TIMEOUT', 30),
        ],
        'platforms' => [
            'wechat' => [
                'mock_endpoint' => '/wechat/oauth/authorize',
                'real_api' => [
                    'app_id' => env('WECHAT_APP_ID', ''),
                    'app_secret' => env('WECHAT_APP_SECRET', ''),
                ],
            ],
            'alipay' => [
                'mock_endpoint' => '/alipay/trade/page/pay',
                'real_api' => [
                    'app_id' => env('ALIPAY_APP_ID', ''),
                    'private_key' => env('ALIPAY_PRIVATE_KEY', ''),
                ],
            ],
            'sms' => [
                'mock_endpoint' => '/sms/aliyun/send',
                'real_api' => [
                    'access_key' => env('ALIYUN_ACCESS_KEY', ''),
                    'access_secret' => env('ALIYUN_ACCESS_SECRET', ''),
                ],
            ],
            'email' => [
                'mock_endpoint' => '/email/smtp/send',
                'real_api' => [
                    'smtp_host' => env('MAIL_HOST', ''),
                    'smtp_username' => env('MAIL_USERNAME', ''),
                    'smtp_password' => env('MAIL_PASSWORD', ''),
                ],
            ],
        ],
    ],
];
