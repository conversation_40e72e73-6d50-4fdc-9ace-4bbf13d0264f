<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\AiGenerationTask;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 音频处理服务
 * 第2D3阶段：音频处理模块
 */
class AudioService
{
    protected $pointsService;

    public function __construct(PointsService $pointsService)
    {
        $this->pointsService = $pointsService;
    }

    /**
     * 音频混音
     */
    public function mixAudio(int $userId, array $audioUrls, ?int $projectId = null, array $mixConfig = []): array
    {
        try {
            DB::beginTransaction();

            // 🚨 升级：验证用户选择的平台
            $platform = $mixConfig['platform'] ?? null;
            $taskType = 'sound_generation'; // 音频混合归类为音效生成

            if ($platform) {
                // 用户指定了平台，验证其有效性
                $validation = \App\Services\AiServiceClient::validatePlatformChoice($platform, $taskType);
                if (!$validation['valid']) {
                    return [
                        'code' => ApiCodeEnum::VALIDATION_ERROR,
                        'message' => $validation['error'],
                        'data' => [
                            'supported_platforms' => $validation['supported_platforms'] ?? []
                        ]
                    ];
                }
            } else {
                // 用户未指定平台，使用默认推荐
                $recommendations = \App\Services\AiServiceClient::getUserRecommendations($userId, $taskType, 1);
                if ($recommendations['success'] && !empty($recommendations['data']['recommendations'])) {
                    $platform = $recommendations['data']['recommendations'][0]['platform_key'];
                } else {
                    $platform = 'volcengine'; // 默认平台
                }
            }

            // 计算预估成本
            $estimatedCost = $this->calculateMixCost(count($audioUrls));

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'audio_mix',
                null,
                300 // 5分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建处理任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => null,
                'task_type' => 'audio_mix',
                'platform' => $platform, // 🚨 升级：使用验证后的平台
                'model_name' => 'audio_mixer',
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'audio_urls' => $audioUrls,
                    'mix_config' => $mixConfig,
                    'user_selected_platform' => !empty($mixConfig['platform']) // 🚨 升级：记录是否用户主动选择
                ],
                'generation_params' => $mixConfig,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行混音任务
            $this->executeAudioMix($task);

            Log::info('音频混音任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'audio_count' => count($audioUrls),
                'cost' => $estimatedCost,
                'user_selected' => !empty($mixConfig['platform'])
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '音频混音任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'platform' => $platform,
                    'user_selected_platform' => !empty($mixConfig['platform']),
                    'estimated_cost' => $estimatedCost
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'audio_urls_count' => is_array($audioUrls) ? count($audioUrls) : 0,
                'project_id' => $projectId,
                'mix_config_keys' => is_array($mixConfig) ? array_keys($mixConfig) : [],
            ];

            Log::error('音频混音失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '音频混音失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取音频混音状态
     */
    public function getAudioMixStatus(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', 'audio_mix')
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            $data = [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'status' => $task->status,
                'cost' => $task->cost,
                'processing_time_ms' => $task->processing_time_ms,
                'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
            ];

            // 如果任务完成，添加生成结果
            if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
                $data['audio_url'] = $task->output_data['audio_url'] ?? '';
                $data['duration'] = $task->output_data['duration'] ?? 0;
                $data['file_size'] = $task->output_data['file_size'] ?? '';
            }

            // 如果任务失败，添加错误信息
            if ($task->status === AiGenerationTask::STATUS_FAILED) {
                $data['error_message'] = $task->error_message;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取音频混音状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取音频混音状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 音频增强
     */
    public function enhanceAudio(int $userId, string $audioUrl, ?int $projectId = null, array $enhanceConfig = []): array
    {
        try {
            DB::beginTransaction();

            // 🚨 升级：验证用户选择的平台
            $platform = $enhanceConfig['platform'] ?? null;
            $taskType = 'sound_generation'; // 音频增强归类为音效生成

            if ($platform) {
                // 用户指定了平台，验证其有效性
                $validation = \App\Services\AiServiceClient::validatePlatformChoice($platform, $taskType);
                if (!$validation['valid']) {
                    return [
                        'code' => ApiCodeEnum::VALIDATION_ERROR,
                        'message' => $validation['error'],
                        'data' => [
                            'supported_platforms' => $validation['supported_platforms'] ?? []
                        ]
                    ];
                }
            } else {
                // 用户未指定平台，使用默认推荐
                $recommendations = \App\Services\AiServiceClient::getUserRecommendations($userId, $taskType, 1);
                if ($recommendations['success'] && !empty($recommendations['data']['recommendations'])) {
                    $platform = $recommendations['data']['recommendations'][0]['platform_key'];
                } else {
                    $platform = 'volcengine'; // 默认平台
                }
            }

            // 计算预估成本
            $estimatedCost = $this->calculateEnhanceCost();

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'audio_enhance',
                null,
                300 // 5分钟超时
            );

            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                return $freezeResult;
            }

            // 创建处理任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => null,
                'task_type' => 'audio_enhance',
                'platform' => $platform, // 🚨 升级：使用验证后的平台
                'model_name' => 'audio_enhancer',
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'audio_url' => $audioUrl,
                    'enhance_config' => $enhanceConfig,
                    'user_selected_platform' => !empty($enhanceConfig['platform']) // 🚨 升级：记录是否用户主动选择
                ],
                'generation_params' => $enhanceConfig,
                'cost' => $estimatedCost
            ]);

            DB::commit();

            // 异步执行增强任务
            $this->executeAudioEnhance($task);

            Log::info('音频增强任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'platform' => $platform,
                'cost' => $estimatedCost,
                'user_selected' => !empty($enhanceConfig['platform'])
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '音频增强任务创建成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'platform' => $platform,
                    'user_selected_platform' => !empty($enhanceConfig['platform']), // 🚨 升级：添加用户选择信息
                    'estimated_cost' => $estimatedCost
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'audio_url' => $audioUrl,
                'project_id' => $projectId,
                'enhance_config_keys' => is_array($enhanceConfig) ? array_keys($enhanceConfig) : [],
            ];

            Log::error('音频增强失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '音频增强失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取音频增强状态
     */
    public function getAudioEnhanceStatus(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::where('id', $taskId)
                ->where('user_id', $userId)
                ->where('task_type', 'audio_enhance')
                ->first();

            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            $data = [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'status' => $task->status,
                'cost' => $task->cost,
                'processing_time_ms' => $task->processing_time_ms,
                'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                'completed_at' => $task->completed_at ? $task->completed_at->format('Y-m-d H:i:s') : null
            ];

            // 如果任务完成，添加生成结果
            if ($task->status === AiGenerationTask::STATUS_COMPLETED && $task->output_data) {
                $data['audio_url'] = $task->output_data['audio_url'] ?? '';
                $data['duration'] = $task->output_data['duration'] ?? 0;
                $data['file_size'] = $task->output_data['file_size'] ?? '';
            }

            // 如果任务失败，添加错误信息
            if ($task->status === AiGenerationTask::STATUS_FAILED) {
                $data['error_message'] = $task->error_message;
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $data
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取音频增强状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取音频增强状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 计算混音成本
     */
    private function calculateMixCost(int $audioCount): float
    {
        return round(0.01 * $audioCount, 4); // 每个音频文件0.01积分
    }

    /**
     * 计算增强成本
     */
    private function calculateEnhanceCost(): float
    {
        return 0.08; // 固定成本
    }

    /**
     * 执行音频混音
     *
     * 🚨 升级：支持用户平台选择和偏好记录
     */
    private function executeAudioMix(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 🚨 升级：调用AI服务进行音频混合
            $result = $this->callAiServiceForAudioMix($task);

            if ($result['success']) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'output_data' => $result['data'],
                    'completed_at' => Carbon::now(),
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
                ]);

                // 确认积分消费
                $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'audio_mix', $task->id);
            } else {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);

                // 返还积分
                $this->pointsService->refundPoints($task->user_id, $task->cost, 'audio_mix_failed', $task->id);
            }

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'audio_mix_error', $task->id);

            Log::error('音频混音执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 执行音频增强
     *
     * 🚨 升级：支持用户平台选择和偏好记录
     */
    private function executeAudioEnhance(AiGenerationTask $task): void
    {
        try {
            $task->update([
                'status' => AiGenerationTask::STATUS_PROCESSING,
                'started_at' => Carbon::now()
            ]);

            // 🚨 升级：调用AI服务进行音频增强
            $result = $this->callAiServiceForAudioEnhance($task);

            if ($result['success']) {
                $task->update([
                    'status' => AiGenerationTask::STATUS_COMPLETED,
                    'output_data' => $result['data'],
                    'completed_at' => Carbon::now(),
                    'processing_time_ms' => Carbon::now()->diffInMilliseconds($task->started_at)
                ]);

                // 确认积分消费
                $this->pointsService->confirmPointsUsage($task->user_id, $task->cost, 'audio_enhance', $task->id);
            } else {
                $task->update([
                    'status' => AiGenerationTask::STATUS_FAILED,
                    'error_message' => $result['error'],
                    'completed_at' => Carbon::now()
                ]);

                // 返还积分
                $this->pointsService->refundPoints($task->user_id, $task->cost, 'audio_enhance_failed', $task->id);
            }

        } catch (\Exception $e) {
            $task->update([
                'status' => AiGenerationTask::STATUS_FAILED,
                'error_message' => $e->getMessage(),
                'completed_at' => Carbon::now()
            ]);

            // 返还积分
            $this->pointsService->refundPoints($task->user_id, $task->cost, 'audio_enhance_error', $task->id);

            Log::error('音频增强执行失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 调用AI服务进行音频混合
     *
     * 🚨 升级：支持用户平台选择和偏好记录
     */
    private function callAiServiceForAudioMix(AiGenerationTask $task): array
    {
        try {
            $taskType = 'sound_generation';
            $requestData = [
                'audio_urls' => $task->input_data['audio_urls'],
                'mix_config' => $task->input_data['mix_config']
            ];

            // 🚨 升级：使用 callWithUserChoice 方法，支持用户偏好记录
            $response = \App\Services\AiServiceClient::callWithUserChoice(
                $task->platform,
                $taskType,
                $requestData,
                $task->user_id
            );

            if ($response['success']) {
                $data = $response['data'];

                return [
                    'success' => true,
                    'data' => [
                        'audio_url' => $data['data']['audio_url'] ?? 'https://aiapi.tiptop.cn/audio/mixed/' . $task->id . '.mp3',
                        'duration' => $data['data']['duration'] ?? 120,
                        'file_size' => $data['data']['file_size'] ?? '5.2MB',
                        'format' => $data['data']['format'] ?? 'mp3',
                        'bitrate' => $data['data']['bitrate'] ?? '128kbps',
                        'mode' => $response['mode'],
                        'user_choice' => $response['user_choice'] ?? null
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI服务调用失败：' . ($response['error'] ?? '未知错误'),
                    'code' => $response['code'] ?? 'UNKNOWN_ERROR'
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'AI服务调用异常：' . $e->getMessage(),
                'code' => 'EXCEPTION_ERROR'
            ];
        }
    }

    /**
     * 调用AI服务进行音频增强
     *
     * 🚨 升级：支持用户平台选择和偏好记录
     */
    private function callAiServiceForAudioEnhance(AiGenerationTask $task): array
    {
        try {
            $taskType = 'sound_generation';
            $requestData = [
                'audio_url' => $task->input_data['audio_url'],
                'enhance_config' => $task->input_data['enhance_config']
            ];

            // 🚨 升级：使用 callWithUserChoice 方法，支持用户偏好记录
            $response = \App\Services\AiServiceClient::callWithUserChoice(
                $task->platform,
                $taskType,
                $requestData,
                $task->user_id
            );

            if ($response['success']) {
                $data = $response['data'];

                return [
                    'success' => true,
                    'data' => [
                        'audio_url' => $data['data']['audio_url'] ?? 'https://aiapi.tiptop.cn/audio/enhanced/' . $task->id . '.mp3',
                        'duration' => $data['data']['duration'] ?? $task->input_data['enhance_config']['duration'] ?? 60,
                        'file_size' => $data['data']['file_size'] ?? '3.8MB',
                        'format' => $data['data']['format'] ?? 'mp3',
                        'bitrate' => $data['data']['bitrate'] ?? '192kbps',
                        'mode' => $response['mode'],
                        'user_choice' => $response['user_choice'] ?? null
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'AI服务调用失败：' . ($response['error'] ?? '未知错误'),
                    'code' => $response['code'] ?? 'UNKNOWN_ERROR'
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'AI服务调用异常：' . $e->getMessage(),
                'code' => 'EXCEPTION_ERROR'
            ];
        }
    }
}
