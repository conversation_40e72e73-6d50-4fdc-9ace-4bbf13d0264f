<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WEB网页工具1: 用户注册流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>📝 WEB网页工具1: 用户注册流程（环境切换优化版）</h1>
        <div class="mermaid">
sequenceDiagram
    participant W as WEB网页工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant TP as 第三方服务

    W->>A: 用户注册请求(用户名/邮箱/密码)
    A->>DB: 检查用户名/邮箱是否存在
    alt 用户已存在
        DB->>A: 返回用户已存在
        A->>W: 返回注册失败(用户已存在)
    else 用户不存在
        A->>A: 密码加密处理
        A->>DB: 创建新用户记录(状态:待验证)
        A->>TP: 发送验证邮件/短信
        TP->>A: 返回发送结果
        A->>R: 缓存验证码(5分钟过期)
        A->>W: 返回注册成功(待验证)

        Note over W: 用户输入验证码
        W->>A: 验证请求(验证码)
        A->>R: 验证验证码有效性
        alt 验证码有效
            A->>DB: 激活用户账户(状态:正常)
            A->>R: 清除验证码缓存
            A->>W: 返回验证成功
        else 验证码无效或过期
            A->>W: 返回验证失败(重新发送)
        end
    end
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
