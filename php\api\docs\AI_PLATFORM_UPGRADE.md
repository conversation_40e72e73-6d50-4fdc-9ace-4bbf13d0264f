# AI平台选择功能升级文档

## 概述

本次升级为所有AI生成接口添加了用户平台选择功能，允许用户主动选择AI平台或使用智能推荐系统。

## 🚨 升级内容

### 1. 核心功能升级

#### 1.1 用户平台选择
- **主动选择**：用户可以在请求中指定 `platform` 参数
- **智能推荐**：未指定平台时，系统根据用户历史偏好推荐最佳平台
- **动态验证**：平台选择验证基于配置文件动态生成，支持灵活扩展

#### 1.2 用户偏好记录
- **使用统计**：记录用户对各平台的使用频次
- **效果评估**：跟踪用户对生成结果的满意度
- **个性化推荐**：基于历史数据提供个性化平台推荐

### 2. 升级的接口模块

#### 2.1 图像生成 (ImageController)
- ✅ 支持平台选择：liblib, kling, minimax
- ✅ 新增接口：
  - `GET /py-api/ai-models/platform-options/image_generation`
  - `GET /py-api/ai-models/user-recommendations/image_generation`

#### 2.2 视频生成 (VideoController)
- ✅ 支持平台选择：kling, minimax
- ✅ 新增接口：
  - `GET /py-api/ai-models/platform-options/video_generation`
  - `GET /py-api/ai-models/user-recommendations/video_generation`

#### 2.3 音频处理 (AudioController)
- ✅ 支持平台选择：volcengine, minimax
- ✅ 新增接口：
  - `GET /py-api/ai-models/platform-options/sound_generation`
  - `GET /py-api/ai-models/user-recommendations/sound_generation`

#### 2.4 语音合成 (VoiceController)
- ✅ 支持平台选择：minimax, volcengine
- ✅ 新增接口：
  - `GET /py-api/ai-models/platform-options/voice_synthesis`
  - `GET /py-api/ai-models/user-recommendations/voice_synthesis`

#### 2.5 音乐生成 (MusicController)
- ✅ 支持平台选择：minimax, volcengine
- ✅ 新增接口：
  - `GET /py-api/ai-models/platform-options/music_generation`
  - `GET /py-api/ai-models/user-recommendations/music_generation`

#### 2.6 故事生成 (StoryController)
- ✅ 支持平台选择：deepseek, minimax
- ✅ 新增接口：
  - `GET /py-api/ai-models/platform-options/text_generation`
  - `GET /py-api/ai-models/user-recommendations/text_generation`

#### 2.7 音效生成 (SoundController)
- ✅ 支持平台选择：volcengine, minimax
- ✅ 共享音频处理接口（sound_generation类型）

### 3. 技术架构升级

#### 3.1 AiServiceClient 升级
- **新增方法**：`callWithUserChoice()` - 支持用户偏好记录的AI服务调用
- **平台验证**：`validatePlatformChoice()` - 验证用户选择的平台有效性
- **选项获取**：`getPlatformOptions()` - 获取可用平台选项
- **推荐系统**：`getUserRecommendations()` - 基于用户偏好的智能推荐

#### 3.2 数据库升级
- **用户偏好表**：记录用户对各平台的使用偏好
- **使用统计表**：跟踪平台使用频次和效果评估
- **任务记录增强**：在生成任务中记录用户是否主动选择平台

#### 3.3 配置文件升级
- **动态平台配置**：`config/ai.php` 支持灵活的平台能力配置
- **任务类型映射**：每个平台明确支持的任务类型
- **默认平台设置**：为每种任务类型配置默认推荐平台

## 🚨 使用方法

### 1. 获取平台选项

```http
GET /py-api/ai-models/platform-options/{task_type}
Authorization: Bearer {token}
```

**支持的任务类型**：
- `image_generation` - 图像生成
- `video_generation` - 视频生成
- `voice_synthesis` - 语音合成
- `sound_generation` - 音频/音效生成
- `music_generation` - 音乐生成
- `text_generation` - 文本/故事生成

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "task_type": "image_generation",
    "platforms": [
      {
        "platform_key": "liblib",
        "name": "LiblibAI",
        "description": "图像生成专业平台",
        "pricing": {"cost_per_request": 0.002, "currency": "CNY"},
        "performance": {"estimated_time": "10-30秒"},
        "features": ["高质量图像", "多种风格", "快速生成"],
        "availability": {"status": "available", "mode": "mock"}
      }
    ],
    "total_count": 3
  }
}
```

### 2. 获取个性化推荐

```http
GET /py-api/ai-models/user-recommendations/{task_type}?limit=3
Authorization: Bearer {token}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "task_type": "image_generation",
    "recommendations": [
      {
        "platform_key": "liblib",
        "name": "LiblibAI",
        "recommendation_reason": "基于您的使用习惯，该平台生成效果最符合您的偏好",
        "personalization": {
          "is_preferred": true,
          "usage_count": 15,
          "recommendation_score": 0.95
        }
      }
    ],
    "total_available": 3
  }
}
```

### 3. 在生成请求中指定平台

所有生成接口现在都支持可选的 `platform` 参数：

```http
POST /py-api/images/generate
Authorization: Bearer {token}
Content-Type: application/json

{
  "prompt": "一只可爱的小猫",
  "platform": "liblib",
  "aspect_ratio": "16:9",
  "quality": "high"
}
```

**响应增强**：
```json
{
  "code": 200,
  "message": "图像生成任务创建成功",
  "data": {
    "task_id": 123,
    "status": "pending",
    "platform": "liblib",
    "user_selected_platform": true,
    "estimated_cost": "0.0020"
  }
}
```

## 🚨 兼容性说明

### 向后兼容
- **现有接口**：所有现有接口保持完全兼容
- **默认行为**：未指定平台时使用智能推荐，确保现有客户端正常工作
- **响应格式**：在现有响应基础上增加平台相关字段，不影响现有解析逻辑

### 新增字段
所有生成接口的响应中新增以下字段：
- `platform`: 实际使用的AI平台
- `user_selected_platform`: 是否用户主动选择平台

## 🚨 配置说明

### 环境变量
无需新增环境变量，使用现有的AI平台配置。

### 配置文件
`config/ai.php` 中的平台配置已更新，支持：
- 每个平台的支持能力声明
- 默认平台推荐配置
- 动态平台验证

## 🚨 监控和日志

### 新增日志
- 用户平台选择行为记录
- 平台推荐命中率统计
- 用户偏好变化趋势

### 性能监控
- 各平台响应时间对比
- 用户满意度统计
- 平台使用分布分析

## 🚨 后续规划

1. **机器学习优化**：基于更多用户数据优化推荐算法
2. **A/B测试支持**：支持平台效果对比测试
3. **成本优化**：基于成本和效果的智能平台选择
4. **实时切换**：支持平台故障时的自动切换机制

---

**升级完成时间**：2025-08-04  
**版本**：v2.1.0  
**负责人**：AI开发团队
