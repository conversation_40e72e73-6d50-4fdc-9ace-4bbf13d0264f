<?php

namespace App\Services\PyApi;

use App\Helpers\ApiTokenHelper;
use App\Exceptions\TokenException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class DeviceTokenService
{
    /**
     * 为设备生成Token
     *
     * @param int $userId
     * @param array $deviceInfo
     * @return array
     */
    public static function generateDeviceToken(int $userId, array $deviceInfo): array
    {
        // 检查设备数量限制
        $maxDevices = config('auth.max_devices_per_user', 5);
        $currentDevices = self::getUserDeviceCount($userId);
        
        if ($currentDevices >= $maxDevices) {
            throw TokenException::deviceLimitExceeded($maxDevices);
        }

        // 生成设备ID
        $deviceId = self::generateDeviceId($deviceInfo);
        
        // 检查设备是否已存在
        $existingDevice = self::getDeviceInfo($userId, $deviceId);
        if ($existingDevice) {
            // 设备已存在，撤销旧Token
            self::revokeDeviceToken($userId, $deviceId);
        }

        // 生成新Token
        $token = ApiTokenHelper::generateToken($userId);
        $ttl = config('auth.token_ttl_days', 30) * 24 * 3600;

        // 存储Token到Redis
        $tokenKey = "user:token:{$userId}:device:{$deviceId}";
        Redis::setex($tokenKey, $ttl, ApiTokenHelper::encryptToken($token));

        // 存储设备信息
        $deviceData = [
            'user_id' => $userId,
            'device_id' => $deviceId,
            'device_name' => $deviceInfo['device_name'] ?? 'Unknown Device',
            'device_type' => $deviceInfo['device_type'] ?? 'unknown',
            'os' => $deviceInfo['os'] ?? 'unknown',
            'browser' => $deviceInfo['browser'] ?? 'unknown',
            'ip_address' => $deviceInfo['ip_address'] ?? request()->ip(),
            'user_agent' => $deviceInfo['user_agent'] ?? request()->userAgent(),
            'created_at' => date('Y-m-d H:i:s'),
            'last_used_at' => date('Y-m-d H:i:s'),
            'status' => 'active'
        ];

        $deviceKey = "user:devices:{$userId}:{$deviceId}";
        Redis::setex($deviceKey, $ttl + 86400, json_encode($deviceData)); // 设备信息比Token多保留1天

        // 记录日志
        Log::info('设备Token生成', [
            'user_id' => $userId,
            'device_id' => $deviceId,
            'device_name' => $deviceData['device_name'],
            'ip_address' => $deviceData['ip_address']
        ]);

        return [
            'token' => $token,
            'device_id' => $deviceId,
            'expires_in' => $ttl,
            'device_info' => $deviceData
        ];
    }

    /**
     * 验证设备Token
     *
     * @param string $token
     * @return array
     */
    public static function validateDeviceToken(string $token): array
    {
        // 解析Token
        $tokenData = ApiTokenHelper::parseToken($token);
        if (!$tokenData) {
            return [
                'success' => false,
                'error' => TokenException::invalidFormat($token)
            ];
        }

        $userId = $tokenData['user_id'];

        // 查找Token对应的设备
        $deviceId = self::findDeviceByToken($userId, $token);
        if (!$deviceId) {
            return [
                'success' => false,
                'error' => TokenException::notFound()
            ];
        }

        // 验证Token
        $tokenKey = "user:token:{$userId}:device:{$deviceId}";
        $storedToken = Redis::get($tokenKey);
        
        if ($storedToken !== ApiTokenHelper::encryptToken($token)) {
            return [
                'success' => false,
                'error' => TokenException::notFound()
            ];
        }

        // 更新设备最后使用时间
        self::updateDeviceLastUsed($userId, $deviceId);

        // 获取设备信息
        $deviceInfo = self::getDeviceInfo($userId, $deviceId);

        return [
            'success' => true,
            'user_id' => $userId,
            'device_id' => $deviceId,
            'device_info' => $deviceInfo
        ];
    }

    /**
     * 获取用户所有设备
     *
     * @param int $userId
     * @return array
     */
    public static function getUserDevices(int $userId): array
    {
        $pattern = "user:devices:{$userId}:*";
        $keys = Redis::keys($pattern);
        
        $devices = [];
        foreach ($keys as $key) {
            $deviceData = Redis::get($key);
            if ($deviceData) {
                $device = json_decode($deviceData, true);
                $deviceId = $device['device_id'];
                
                // 检查Token是否还有效
                $tokenKey = "user:token:{$userId}:device:{$deviceId}";
                $device['token_valid'] = Redis::exists($tokenKey);
                
                $devices[] = $device;
            }
        }

        // 按最后使用时间排序
        usort($devices, function($a, $b) {
            return strtotime($b['last_used_at']) - strtotime($a['last_used_at']);
        });

        return $devices;
    }

    /**
     * 撤销设备Token
     *
     * @param int $userId
     * @param string $deviceId
     * @return bool
     */
    public static function revokeDeviceToken(int $userId, string $deviceId): bool
    {
        $tokenKey = "user:token:{$userId}:device:{$deviceId}";
        $deviceKey = "user:devices:{$userId}:{$deviceId}";
        
        // 删除Token
        Redis::del($tokenKey);
        
        // 更新设备状态
        $deviceData = Redis::get($deviceKey);
        if ($deviceData) {
            $device = json_decode($deviceData, true);
            $device['status'] = 'revoked';
            $device['revoked_at'] = date('Y-m-d H:i:s');
            Redis::setex($deviceKey, 86400, json_encode($device)); // 保留1天用于审计
        }

        Log::info('设备Token撤销', [
            'user_id' => $userId,
            'device_id' => $deviceId
        ]);

        return true;
    }

    /**
     * 撤销用户所有设备Token
     *
     * @param int $userId
     * @return int 撤销的设备数量
     */
    public static function revokeAllUserDeviceTokens(int $userId): int
    {
        $devices = self::getUserDevices($userId);
        $revokedCount = 0;

        foreach ($devices as $device) {
            if ($device['token_valid']) {
                self::revokeDeviceToken($userId, $device['device_id']);
                $revokedCount++;
            }
        }

        return $revokedCount;
    }

    /**
     * 生成设备ID
     *
     * @param array $deviceInfo
     * @return string
     */
    private static function generateDeviceId(array $deviceInfo): string
    {
        $fingerprint = implode('|', [
            $deviceInfo['device_type'] ?? 'unknown',
            $deviceInfo['os'] ?? 'unknown',
            $deviceInfo['browser'] ?? 'unknown',
            $deviceInfo['user_agent'] ?? request()->userAgent(),
        ]);
        
        return 'dev_' . substr(md5($fingerprint), 0, 16);
    }

    /**
     * 获取用户设备数量
     *
     * @param int $userId
     * @return int
     */
    private static function getUserDeviceCount(int $userId): int
    {
        $pattern = "user:devices:{$userId}:*";
        $keys = Redis::keys($pattern);
        
        $activeCount = 0;
        foreach ($keys as $key) {
            $deviceData = Redis::get($key);
            if ($deviceData) {
                $device = json_decode($deviceData, true);
                if ($device['status'] === 'active') {
                    $activeCount++;
                }
            }
        }
        
        return $activeCount;
    }

    /**
     * 通过Token查找设备ID
     *
     * @param int $userId
     * @param string $token
     * @return string|null
     */
    private static function findDeviceByToken(int $userId, string $token): ?string
    {
        $pattern = "user:token:{$userId}:device:*";
        $keys = Redis::keys($pattern);
        
        $encryptedToken = ApiTokenHelper::encryptToken($token);
        
        foreach ($keys as $key) {
            $storedToken = Redis::get($key);
            if ($storedToken === $encryptedToken) {
                // 从key中提取device_id
                $parts = explode(':', $key);
                return end($parts);
            }
        }
        
        return null;
    }

    /**
     * 获取设备信息
     *
     * @param int $userId
     * @param string $deviceId
     * @return array|null
     */
    private static function getDeviceInfo(int $userId, string $deviceId): ?array
    {
        $deviceKey = "user:devices:{$userId}:{$deviceId}";
        $deviceData = Redis::get($deviceKey);
        
        return $deviceData ? json_decode($deviceData, true) : null;
    }

    /**
     * 更新设备最后使用时间
     *
     * @param int $userId
     * @param string $deviceId
     */
    private static function updateDeviceLastUsed(int $userId, string $deviceId): void
    {
        $deviceKey = "user:devices:{$userId}:{$deviceId}";
        $deviceData = Redis::get($deviceKey);
        
        if ($deviceData) {
            $device = json_decode($deviceData, true);
            $device['last_used_at'] = date('Y-m-d H:i:s');
            
            $ttl = Redis::ttl($deviceKey);
            if ($ttl > 0) {
                Redis::setex($deviceKey, $ttl, json_encode($device));
            }
        }
    }
}
