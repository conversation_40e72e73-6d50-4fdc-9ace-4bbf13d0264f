<?php

/**
 * 导出完整的数据库结构信息
 * 包含表名、字段、注释、索引、外键关系等
 */

// require_once 'vendor/autoload.php';

try {
    // 数据库连接配置
    $host = '127.0.0.1';
    $port = 3306;
    $database = 'ai_tool';
    $username = 'root';
    $password = 'rootroot';
    
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功\n";
    echo "📋 数据库: $database\n\n";
    
    // 获取所有表
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📊 数据库结构导出报告\n";
    echo "=" . str_repeat("=", 80) . "\n\n";
    
    $totalTables = count($tables);
    echo "📋 总表数量: $totalTables\n\n";
    
    $tableStructures = [];
    
    foreach ($tables as $table) {
        echo "🔍 分析表: $table\n";
        
        $tableInfo = [
            'name' => $table,
            'comment' => '',
            'fields' => [],
            'indexes' => [],
            'foreign_keys' => [],
            'row_count' => 0,
            'engine' => '',
            'charset' => '',
            'created_at' => '',
        ];
        
        // 获取表信息
        $tableInfoStmt = $pdo->query("
            SELECT 
                TABLE_COMMENT,
                ENGINE,
                TABLE_COLLATION,
                CREATE_TIME
            FROM information_schema.TABLES 
            WHERE TABLE_SCHEMA = '$database' AND TABLE_NAME = '$table'
        ");
        $tableInfoData = $tableInfoStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($tableInfoData) {
            $tableInfo['comment'] = $tableInfoData['TABLE_COMMENT'] ?? '';
            $tableInfo['engine'] = $tableInfoData['ENGINE'] ?? '';
            $tableInfo['charset'] = $tableInfoData['TABLE_COLLATION'] ?? '';
            $tableInfo['created_at'] = $tableInfoData['CREATE_TIME'] ?? '';
        }
        
        // 获取表行数
        try {
            $countStmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
            $tableInfo['row_count'] = $countStmt->fetch()['count'];
        } catch (Exception $e) {
            $tableInfo['row_count'] = 'N/A';
        }
        
        // 获取字段信息
        $fieldsStmt = $pdo->query("
            SELECT 
                COLUMN_NAME,
                COLUMN_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_COMMENT,
                EXTRA,
                COLUMN_KEY
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = '$database' AND TABLE_NAME = '$table'
            ORDER BY ORDINAL_POSITION
        ");
        
        while ($field = $fieldsStmt->fetch(PDO::FETCH_ASSOC)) {
            $tableInfo['fields'][] = [
                'name' => $field['COLUMN_NAME'],
                'type' => $field['COLUMN_TYPE'],
                'nullable' => $field['IS_NULLABLE'] === 'YES',
                'default' => $field['COLUMN_DEFAULT'],
                'comment' => $field['COLUMN_COMMENT'],
                'extra' => $field['EXTRA'],
                'key' => $field['COLUMN_KEY']
            ];
        }
        
        // 获取索引信息
        $indexStmt = $pdo->query("SHOW INDEX FROM `$table`");
        $indexes = [];
        while ($index = $indexStmt->fetch(PDO::FETCH_ASSOC)) {
            $indexName = $index['Key_name'];
            if (!isset($indexes[$indexName])) {
                $indexes[$indexName] = [
                    'name' => $indexName,
                    'unique' => !$index['Non_unique'],
                    'type' => $index['Index_type'],
                    'columns' => []
                ];
            }
            $indexes[$indexName]['columns'][] = $index['Column_name'];
        }
        $tableInfo['indexes'] = array_values($indexes);
        
        // 获取外键信息
        $fkStmt = $pdo->query("
            SELECT 
                CONSTRAINT_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME,
                UPDATE_RULE,
                DELETE_RULE
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = '$database' 
            AND TABLE_NAME = '$table' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        
        while ($fk = $fkStmt->fetch(PDO::FETCH_ASSOC)) {
            $tableInfo['foreign_keys'][] = [
                'constraint_name' => $fk['CONSTRAINT_NAME'],
                'column' => $fk['COLUMN_NAME'],
                'referenced_table' => $fk['REFERENCED_TABLE_NAME'],
                'referenced_column' => $fk['REFERENCED_COLUMN_NAME'],
                'update_rule' => $fk['UPDATE_RULE'],
                'delete_rule' => $fk['DELETE_RULE']
            ];
        }
        
        $tableStructures[] = $tableInfo;
    }
    
    // 输出详细结构信息
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "📋 详细表结构信息\n";
    echo str_repeat("=", 80) . "\n\n";
    
    foreach ($tableStructures as $table) {
        echo "📊 表名: {$table['name']}\n";
        echo "💬 注释: " . ($table['comment'] ?: '无') . "\n";
        echo "🔧 引擎: {$table['engine']}\n";
        echo "📝 字符集: {$table['charset']}\n";
        echo "📈 行数: {$table['row_count']}\n";
        echo "📅 创建时间: " . ($table['created_at'] ?: '未知') . "\n";
        
        echo "\n🔹 字段信息:\n";
        foreach ($table['fields'] as $field) {
            $nullable = $field['nullable'] ? 'NULL' : 'NOT NULL';
            $default = $field['default'] !== null ? "DEFAULT '{$field['default']}'" : '';
            $extra = $field['extra'] ? $field['extra'] : '';
            $key = $field['key'] ? "({$field['key']})" : '';
            
            echo sprintf(
                "  %-20s %-20s %-10s %-15s %-15s %s %s\n",
                $field['name'],
                $field['type'],
                $nullable,
                $default,
                $extra,
                $key,
                $field['comment'] ? "// {$field['comment']}" : ''
            );
        }
        
        if (!empty($table['indexes'])) {
            echo "\n🔹 索引信息:\n";
            foreach ($table['indexes'] as $index) {
                $unique = $index['unique'] ? 'UNIQUE' : '';
                $columns = implode(', ', $index['columns']);
                echo "  {$index['name']}: $unique {$index['type']} ($columns)\n";
            }
        }
        
        if (!empty($table['foreign_keys'])) {
            echo "\n🔹 外键关系:\n";
            foreach ($table['foreign_keys'] as $fk) {
                echo "  {$fk['column']} -> {$fk['referenced_table']}.{$fk['referenced_column']}\n";
                echo "    UPDATE: {$fk['update_rule']}, DELETE: {$fk['delete_rule']}\n";
            }
        }
        
        echo "\n" . str_repeat("-", 80) . "\n\n";
    }
    
    // 保存为JSON文件
    $jsonData = [
        'export_time' => date('Y-m-d H:i:s'),
        'database' => $database,
        'total_tables' => $totalTables,
        'tables' => $tableStructures
    ];
    
    file_put_contents('database_structure_export.json', json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    echo "✅ 数据库结构导出完成\n";
    echo "📄 详细信息已保存到: database_structure_export.json\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    exit(1);
}
