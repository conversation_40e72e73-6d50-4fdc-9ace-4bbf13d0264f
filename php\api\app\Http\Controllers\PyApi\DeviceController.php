<?php

namespace App\Http\Controllers\PyApi;

use App\Http\Controllers\Controller;
use App\Services\PyApi\AuthService;
use App\Services\PyApi\DeviceTokenService;
use App\Enums\ApiCodeEnum;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DeviceController extends Controller
{
    /**
     * 获取用户设备列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDevices(Request $request): JsonResponse
    {
        try {
            // 认证用户
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return response()->json($authResult['response']);
            }

            $user = $authResult['user'];
            $devices = DeviceTokenService::getUserDevices($user->id);

            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取设备列表成功',
                'data' => [
                    'devices' => $devices,
                    'total' => count($devices),
                    'max_devices' => config('auth.max_devices_per_user', 5)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '获取设备列表失败',
                'data' => []
            ]);
        }
    }

    /**
     * 撤销指定设备Token
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function revokeDevice(Request $request): JsonResponse
    {
        try {
            // 认证用户
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return response()->json($authResult['response']);
            }

            $user = $authResult['user'];
            $deviceId = $request->input('device_id');

            if (empty($deviceId)) {
                return response()->json([
                    'code' => ApiCodeEnum::PARAM_ERROR,
                    'message' => '设备ID不能为空',
                    'data' => []
                ]);
            }

            $success = DeviceTokenService::revokeDeviceToken($user->id, $deviceId);

            if ($success) {
                return response()->json([
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '设备Token撤销成功',
                    'data' => []
                ]);
            } else {
                return response()->json([
                    'code' => ApiCodeEnum::SYSTEM_ERROR,
                    'message' => '设备Token撤销失败',
                    'data' => []
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '撤销设备失败',
                'data' => []
            ]);
        }
    }

    /**
     * 撤销所有设备Token（除当前设备）
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function revokeAllDevices(Request $request): JsonResponse
    {
        try {
            // 认证用户
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return response()->json($authResult['response']);
            }

            $user = $authResult['user'];
            $currentToken = AuthService::extractToken($request);
            
            // 获取当前设备ID
            $currentDeviceId = null;
            if ($currentToken) {
                $validateResult = DeviceTokenService::validateDeviceToken($currentToken);
                if ($validateResult['success']) {
                    $currentDeviceId = $validateResult['device_id'];
                }
            }

            $devices = DeviceTokenService::getUserDevices($user->id);
            $revokedCount = 0;

            foreach ($devices as $device) {
                // 跳过当前设备
                if ($device['device_id'] === $currentDeviceId) {
                    continue;
                }
                
                if ($device['token_valid']) {
                    DeviceTokenService::revokeDeviceToken($user->id, $device['device_id']);
                    $revokedCount++;
                }
            }

            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => "成功撤销 {$revokedCount} 个设备的Token",
                'data' => [
                    'revoked_count' => $revokedCount,
                    'current_device_id' => $currentDeviceId
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '撤销所有设备失败',
                'data' => []
            ]);
        }
    }

    /**
     * 获取Token状态信息
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getTokenStatus(Request $request): JsonResponse
    {
        try {
            $token = AuthService::extractToken($request);
            if (!$token) {
                return response()->json([
                    'code' => ApiCodeEnum::PARAM_ERROR,
                    'message' => 'Token不能为空',
                    'data' => []
                ]);
            }

            // 使用增强的Token验证
            $result = AuthService::validateTokenEnhanced($token);
            
            if ($result['success']) {
                // 获取设备信息
                $deviceResult = DeviceTokenService::validateDeviceToken($token);
                
                return response()->json([
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => 'Token状态查询成功',
                    'data' => [
                        'status' => 'valid',
                        'user_id' => $result['user']->id,
                        'device_info' => $deviceResult['success'] ? $deviceResult['device_info'] : null,
                        'token_info' => AuthService::getTokenStatus($token)
                    ]
                ]);
            } else {
                return response()->json([
                    'code' => $result['error']->getErrorCode(),
                    'message' => $result['error']->getMessage(),
                    'data' => $result['error']->getErrorData()
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => 'Token状态查询失败',
                'data' => []
            ]);
        }
    }

    /**
     * 设备登录（生成设备Token）
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deviceLogin(Request $request): JsonResponse
    {
        try {
            // 这里应该先进行用户名密码验证
            // 为了演示，假设已经验证通过，获得了用户ID
            $userId = $request->input('user_id'); // 实际应该从认证结果获取
            
            if (!$userId) {
                return response()->json([
                    'code' => ApiCodeEnum::PARAM_ERROR,
                    'message' => '用户ID不能为空',
                    'data' => []
                ]);
            }

            // 获取设备信息
            $deviceInfo = [
                'device_name' => $request->input('device_name', 'Unknown Device'),
                'device_type' => $request->input('device_type', 'unknown'),
                'os' => $request->input('os', 'unknown'),
                'browser' => $request->input('browser', 'unknown'),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ];

            $result = DeviceTokenService::generateDeviceToken($userId, $deviceInfo);

            return response()->json([
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '设备登录成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            if ($e instanceof \App\Exceptions\TokenException) {
                return response()->json($e->toResponse());
            }

            return response()->json([
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '设备登录失败',
                'data' => []
            ]);
        }
    }
}
