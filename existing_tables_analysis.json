{"analysis_time": "2025-08-04 21:13:52", "total_tables": 39, "tables": {"p_achievements": {"model_name": "Achievement", "table_name": "p_achievements", "model_table_name": "achievements", "fillable_fields": ["name", "description", "type", "difficulty", "icon", "badge_color", "requirements", "reward_experience", "reward_points", "status", "sort_order", "is_hidden", "unlock_condition", "metadata"], "relationships": [{"method": "userAchievements", "type": "hasMany", "target": "UserAchievement::class"}, {"method": "completedUsers", "type": "belongsToMany", "target": "User::class, 'p_user_achievements'"}], "constants": {"TYPE_CREATION": "creation", "TYPE_INTERACTION": "interaction", "TYPE_CONTINUOUS": "continuous", "TYPE_MILESTONE": "milestone", "STATUS_ACTIVE": "active", "STATUS_INACTIVE": "inactive", "STATUS_DRAFT": "draft", "DIFFICULTY_EASY": "easy", "DIFFICULTY_MEDIUM": "medium", "DIFFICULTY_HARD": "hard", "DIFFICULTY_LEGENDARY": "legendary"}, "casts": {"requirements": "array", "unlock_condition": "array", "metadata": "array", "reward_experience": "integer", "reward_points": "decimal:2", "sort_order": "integer", "is_hidden": "boolean", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/Achievement.php"}, "p_ai_generation_tasks": {"model_name": "AiGenerationTask", "table_name": "p_ai_generation_tasks", "model_table_name": "ai_generation_tasks", "fillable_fields": ["user_id", "project_id", "model_config_id", "task_type", "platform", "model_name", "status", "input_data", "output_data", "generation_params", "external_task_id", "cost", "tokens_used", "processing_time_ms", "started_at", "completed_at", "error_message", "metadata", "retry_count", "max_retries"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "project", "type": "belongsTo", "target": "Project::class"}, {"method": "modelConfig", "type": "belongsTo", "target": "AiModelConfig::class, 'model_config_id'"}], "constants": {"STATUS_PENDING": "pending", "STATUS_PROCESSING": "processing", "STATUS_COMPLETED": "completed", "STATUS_FAILED": "failed", "STATUS_CANCELLED": "cancelled", "STATUS_TIMEOUT": "timeout", "TYPE_TEXT_GENERATION": "text_generation", "TYPE_IMAGE_GENERATION": "image_generation", "TYPE_VIDEO_GENERATION": "video_generation", "TYPE_VOICE_SYNTHESIS": "voice_synthesis", "TYPE_MUSIC_GENERATION": "music_generation", "TYPE_SOUND_GENERATION": "sound_generation", "TYPE_STORY_GENERATION": "story_generation", "TYPE_CHARACTER_GENERATION": "character_generation"}, "casts": {"input_data": "array", "output_data": "array", "generation_params": "array", "cost": "decimal:4", "tokens_used": "integer", "processing_time_ms": "integer", "started_at": "datetime", "completed_at": "datetime", "metadata": "array", "retry_count": "integer", "max_retries": "integer", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/AiGenerationTask.php"}, "p_ai_model_configs": {"model_name": "AiModelConfig", "table_name": "p_ai_model_configs", "model_table_name": "ai_model_configs", "fillable_fields": ["platform", "model_name", "model_type", "api_endpoint", "config_params", "capabilities", "is_active", "is_default", "priority", "cost_per_request", "max_tokens", "timeout_seconds", "rate_limits", "performance_metrics", "last_health_check", "health_status", "health_message"], "relationships": [{"method": "generationTasks", "type": "hasMany", "target": "AiGenerationTask::class, 'model_config_id'"}], "constants": {"PLATFORM_DEEPSEEK": "deepseek", "PLATFORM_LIBLIB": "liblib", "PLATFORM_KLING": "kling", "PLATFORM_MINIMAX": "minimax", "PLATFORM_VOLCENGINE": "volcengine", "TYPE_TEXT_GENERATION": "text_generation", "TYPE_IMAGE_GENERATION": "image_generation", "TYPE_VIDEO_GENERATION": "video_generation", "TYPE_VOICE_SYNTHESIS": "voice_synthesis", "TYPE_MUSIC_GENERATION": "music_generation", "TYPE_SOUND_GENERATION": "sound_generation", "HEALTH_HEALTHY": "healthy", "HEALTH_DEGRADED": "degraded", "HEALTH_UNHEALTHY": "unhealthy", "HEALTH_UNKNOWN": "unknown"}, "casts": {"config_params": "array", "capabilities": "array", "is_active": "boolean", "is_default": "boolean", "priority": "integer", "cost_per_request": "decimal:4", "max_tokens": "integer", "timeout_seconds": "integer", "rate_limits": "array", "performance_metrics": "array", "last_health_check": "datetime", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/AiModelConfig.php"}, "p_character_categories": {"model_name": "CharacterCategory", "table_name": "p_character_categories", "model_table_name": "character_categories", "fillable_fields": ["name", "slug", "description", "icon", "color", "parent_id", "is_active", "sort_order", "character_count", "metadata"], "relationships": [{"method": "parent", "type": "belongsTo", "target": "CharacterCategory::class, 'parent_id'"}, {"method": "children", "type": "hasMany", "target": "CharacterCategory::class, 'parent_id'"}, {"method": "characters", "type": "hasMany", "target": "CharacterLibrary::class, 'category_id'"}], "constants": [], "casts": {"is_active": "boolean", "sort_order": "integer", "character_count": "integer", "metadata": "array", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/CharacterCategory.php"}, "p_character_library": {"model_name": "CharacterLibrary", "table_name": "p_character_library", "model_table_name": "character_library", "fillable_fields": ["name", "description", "category_id", "gender", "age_range", "personality", "background", "appearance", "avatar", "images", "voice_config", "style_preferences", "tags", "is_active", "is_premium", "is_featured", "sort_order", "binding_count", "rating", "rating_count", "created_by"], "relationships": [{"method": "category", "type": "belongsTo", "target": "CharacterCategory::class, 'category_id'"}, {"method": "creator", "type": "belongsTo", "target": "User::class, 'created_by'"}, {"method": "userBindings", "type": "hasMany", "target": "UserCharacterBinding::class, 'character_id'"}], "constants": {"GENDER_MALE": "male", "GENDER_FEMALE": "female", "GENDER_OTHER": "other"}, "casts": {"images": "array", "voice_config": "array", "style_preferences": "array", "tags": "array", "is_active": "boolean", "is_premium": "boolean", "is_featured": "boolean", "sort_order": "integer", "binding_count": "integer", "rating": "decimal:2", "rating_count": "integer", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/CharacterLibrary.php"}, "p_daily_tasks": {"model_name": "DailyTask", "table_name": "p_daily_tasks", "model_table_name": "daily_tasks", "fillable_fields": ["name", "description", "type", "difficulty", "icon", "requirements", "reward_experience", "reward_points", "status", "sort_order", "repeat_type", "max_completions_per_day", "is_premium", "unlock_level", "start_date", "end_date", "metadata"], "relationships": [{"method": "userDailyTasks", "type": "hasMany", "target": "UserDailyTask::class"}, {"method": "completedUsers", "type": "belongsToMany", "target": "User::class, 'p_user_daily_tasks'"}], "constants": {"TYPE_LOGIN": "login", "TYPE_CREATION": "creation", "TYPE_INTERACTION": "interaction", "TYPE_SHARING": "sharing", "TYPE_LEARNING": "learning", "STATUS_ACTIVE": "active", "STATUS_INACTIVE": "inactive", "STATUS_DRAFT": "draft", "STATUS_ARCHIVED": "archived", "DIFFICULTY_EASY": "easy", "DIFFICULTY_MEDIUM": "medium", "DIFFICULTY_HARD": "hard", "REPEAT_DAILY": "daily", "REPEAT_WEEKLY": "weekly", "REPEAT_MONTHLY": "monthly"}, "casts": {"requirements": "array", "metadata": "array", "reward_experience": "integer", "reward_points": "decimal:2", "sort_order": "integer", "max_completions_per_day": "integer", "unlock_level": "integer", "is_premium": "boolean", "start_date": "date", "end_date": "date", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/DailyTask.php"}, "p_p_follows": {"model_name": "Follow", "table_name": "p_p_follows", "model_table_name": "p_follows", "fillable_fields": ["follower_id", "following_id", "status", "followed_at", "metadata"], "relationships": [{"method": "follower", "type": "belongsTo", "target": "User::class, 'follower_id'"}, {"method": "following", "type": "belongsTo", "target": "User::class, 'following_id'"}], "constants": {"STATUS_ACTIVE": "active", "STATUS_BLOCKED": "blocked", "STATUS_PENDING": "pending"}, "casts": {"followed_at": "datetime", "metadata": "array", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/Follow.php"}, "p_growth_histories": {"model_name": "GrowthHistory", "table_name": "p_growth_histories", "model_table_name": "growth_histories", "fillable_fields": ["user_id", "event_type", "event_description", "experience_before", "experience_change", "experience_after", "level_before", "level_after", "points_before", "points_change", "points_after", "related_type", "related_id", "metadata"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}], "constants": {"EVENT_ACHIEVEMENT_UNLOCKED": "achievement_unlocked", "EVENT_TASK_COMPLETED": "task_completed", "EVENT_LEVEL_UP": "level_up", "EVENT_LOGIN_BONUS": "login_bonus", "EVENT_CREATION_BONUS": "creation_bonus", "EVENT_INTERACTION_BONUS": "interaction_bonus", "EVENT_SHARING_BONUS": "sharing_bonus", "EVENT_REFERRAL_BONUS": "referral_bonus", "EVENT_PREMIUM_BONUS": "premium_bonus", "EVENT_MANUAL_ADJUSTMENT": "manual_adjustment", "EVENT_SYSTEM_REWARD": "system_reward", "EVENT_PENALTY": "penalty"}, "casts": {"user_id": "integer", "experience_before": "integer", "experience_change": "integer", "experience_after": "integer", "level_before": "integer", "level_after": "integer", "points_before": "decimal:2", "points_change": "decimal:2", "points_after": "decimal:2", "related_id": "integer", "metadata": "array", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/GrowthHistory.php"}, "p_platform_performance_metrics": {"model_name": "PlatformPerformanceMetric", "table_name": "p_platform_performance_metrics", "model_table_name": "platform_performance_metrics", "fillable_fields": ["platform", "business_type", "response_time_avg", "success_rate", "cost_score", "quality_score", "total_requests", "failed_requests", "uptime_percentage", "detailed_metrics", "metric_date"], "relationships": [], "constants": {"PLATFORM_DEEPSEEK": "deepseek", "PLATFORM_LIBLIB": "liblib", "PLATFORM_KLING": "kling", "PLATFORM_MINIMAX": "minimax", "PLATFORM_VOLCENGINE": "volcengine", "BUSINESS_TYPE_IMAGE": "image", "BUSINESS_TYPE_VIDEO": "video", "BUSINESS_TYPE_STORY": "story", "BUSINESS_TYPE_CHARACTER": "character", "BUSINESS_TYPE_STYLE": "style", "BUSINESS_TYPE_VOICE": "voice", "BUSINESS_TYPE_SOUND": "sound", "BUSINESS_TYPE_MUSIC": "music"}, "casts": {"response_time_avg": "decimal:3", "success_rate": "decimal:4", "cost_score": "decimal:2", "quality_score": "decimal:2", "total_requests": "integer", "failed_requests": "integer", "uptime_percentage": "decimal:2", "detailed_metrics": "array", "metric_date": "date", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/PlatformPerformanceMetric.php"}, "p_platform_usage_statistics": {"model_name": "PlatformUsageStatistic", "table_name": "p_platform_usage_statistics", "model_table_name": "platform_usage_statistics", "fillable_fields": ["user_id", "task_type", "platform", "usage_count", "success_count", "failure_count", "total_cost", "average_response_time", "user_satisfaction_score", "user_rating_count", "performance_metrics", "error_details", "last_used_at"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}], "constants": {"TASK_TYPE_IMAGE_GENERATION": "image_generation", "TASK_TYPE_VIDEO_GENERATION": "video_generation", "TASK_TYPE_VOICE_SYNTHESIS": "voice_synthesis", "TASK_TYPE_SOUND_GENERATION": "sound_generation", "TASK_TYPE_MUSIC_GENERATION": "music_generation", "TASK_TYPE_TEXT_GENERATION": "text_generation"}, "casts": {"usage_count": "integer", "success_count": "integer", "failure_count": "integer", "total_cost": "decimal:4", "average_response_time": "decimal:2", "user_satisfaction_score": "decimal:2", "user_rating_count": "integer", "performance_metrics": "array", "error_details": "array", "last_used_at": "datetime", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/PlatformUsageStatistic.php"}, "p_points_freeze": {"model_name": "PointsFreeze", "table_name": "p_points_freeze", "model_table_name": "points_freeze", "fillable_fields": ["user_id", "transaction_id", "amount", "status", "business_type", "business_id", "expires_at", "released_at", "reason"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "transaction", "type": "belongsTo", "target": "PointsTransaction::class"}], "constants": {"STATUS_FROZEN": "frozen", "STATUS_RELEASED": "released", "STATUS_CONSUMED": "consumed"}, "casts": {"amount": "decimal:2", "expires_at": "datetime", "released_at": "datetime", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/PointsFreeze.php"}, "p_points_transactions": {"model_name": "PointsTransaction", "table_name": "p_points_transactions", "model_table_name": "points_transactions", "fillable_fields": ["user_id", "business_type", "business_id", "amount", "status", "ai_platform", "request_data", "response_data", "timeout_seconds", "completed_at", "failure_reason"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}], "constants": {"STATUS_FROZEN": "frozen", "STATUS_SUCCESS": "success", "STATUS_FAILED": "failed", "STATUS_REFUNDED": "refunded", "TYPE_TEXT_TO_IMAGE": "text_to_image", "TYPE_IMAGE_TO_VIDEO": "image_to_video", "TYPE_TEXT_GENERATION": "text_generation", "TYPE_VOICE_SYNTHESIS": "voice_synthesis", "PLATFORM_DEEPSEEK": "deepseek", "PLATFORM_LIBLIB": "liblib", "PLATFORM_KLING": "kling", "PLATFORM_MINIMAX": "minimax"}, "casts": {"amount": "decimal:2", "request_data": "array", "response_data": "array", "timeout_seconds": "integer", "completed_at": "datetime", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/PointsTransaction.php"}, "p_projects": {"model_name": "Project", "table_name": "p_projects", "model_table_name": "projects", "fillable_fields": ["user_id", "title", "description", "style_id", "story_content", "ai_generated_title", "title_confirmed", "status", "project_config", "metadata", "last_accessed_at", "completed_at", "published_at", "view_count", "is_public"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "style", "type": "belongsTo", "target": "StyleLibrary::class, 'style_id'"}], "constants": {"STATUS_DRAFT": "draft", "STATUS_IN_PROGRESS": "in_progress", "STATUS_COMPLETED": "completed", "STATUS_PUBLISHED": "published", "STATUS_ARCHIVED": "archived", "STATUS_ACTIVE": "active"}, "casts": {"title_confirmed": "boolean", "project_config": "array", "metadata": "array", "last_accessed_at": "datetime", "completed_at": "datetime", "published_at": "datetime", "view_count": "integer", "is_public": "boolean", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/Project.php"}, "p_project_collaborators": {"model_name": "ProjectCollaborator", "table_name": "p_project_collaborators", "model_table_name": "project_collaborators", "fillable_fields": ["project_id", "user_id", "role", "permissions", "status", "invited_by"], "relationships": [{"method": "project", "type": "belongsTo", "target": "Project::class"}, {"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "inviter", "type": "belongsTo", "target": "User::class, 'invited_by'"}], "constants": {"ROLE_VIEWER": "viewer", "ROLE_EDITOR": "editor", "ROLE_ADMIN": "admin", "STATUS_INVITED": "invited", "STATUS_ACTIVE": "active", "STATUS_INACTIVE": "inactive"}, "casts": {"permissions": "array", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/ProjectCollaborator.php"}, "p_publications": {"model_name": "Publication", "table_name": "p_publications", "model_table_name": "publications", "fillable_fields": ["user_id", "resource_id", "title", "description", "tags", "category", "visibility", "status", "review_status", "allow_comments", "allow_download", "featured", "view_count", "like_count", "comment_count", "download_count", "share_count", "thumbnail", "metadata", "published_at", "unpublished_at"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "resource", "type": "belongsTo", "target": "Resource::class"}, {"method": "reviews", "type": "hasMany", "target": "Review::class"}, {"method": "comments", "type": "hasMany", "target": "PublicationComment::class"}, {"method": "likes", "type": "hasMany", "target": "PublicationLike::class"}], "constants": {"STATUS_PENDING_REVIEW": "pending_review", "STATUS_PUBLISHED": "published", "STATUS_REJECTED": "rejected", "STATUS_UNPUBLISHED": "unpublished", "REVIEW_STATUS_PENDING": "pending", "REVIEW_STATUS_APPROVED": "approved", "REVIEW_STATUS_REJECTED": "rejected", "REVIEW_STATUS_APPEALING": "appealing", "VISIBILITY_PUBLIC": "public", "VISIBILITY_PRIVATE": "private", "VISIBILITY_UNLISTED": "unlisted", "CATEGORY_STORY": "story", "CATEGORY_IMAGE": "image", "CATEGORY_VOICE": "voice", "CATEGORY_VIDEO": "video", "CATEGORY_MUSIC": "music", "CATEGORY_SOUND": "sound", "CATEGORY_MIXED": "mixed"}, "casts": {"tags": "array", "metadata": "array", "allow_comments": "boolean", "allow_download": "boolean", "featured": "boolean", "published_at": "datetime", "unpublished_at": "datetime", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/Publication.php"}, "p_p_recommendations": {"model_name": "Recommendation", "table_name": "p_p_recommendations", "model_table_name": "p_recommendations", "fillable_fields": ["user_id", "recommended_type", "recommended_id", "recommendation_type", "algorithm_type", "score", "reason", "status", "expires_at", "metadata"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "feedbacks", "type": "hasMany", "target": "RecommendationFeedback::class"}], "constants": {"STATUS_ACTIVE": "active", "STATUS_INACTIVE": "inactive", "STATUS_EXPIRED": "expired", "STATUS_PROCESSED": "processed", "STATUS_PENDING": "pending", "TYPE_PROJECT": "project", "TYPE_RESOURCE": "resource", "TYPE_USER": "user", "TYPE_CONTENT": "content", "ALGORITHM_COLLABORATIVE": "collaborative", "ALGORITHM_CONTENT_BASED": "content_based", "ALGORITHM_HYBRID": "hybrid", "ALGORITHM_POPULAR": "popular"}, "casts": {"score": "decimal:4", "expires_at": "datetime", "metadata": "array"}, "file_path": "php/api/app/Models/Recommendation.php"}, "p_p_recommendation_feedbacks": {"model_name": "RecommendationFeedback", "table_name": "p_p_recommendation_feedbacks", "model_table_name": "p_recommendation_feedbacks", "fillable_fields": ["user_id", "recommendation_id", "content_type", "content_id", "feedback_type", "status", "score", "comment", "context_data", "processed_at"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "recommendation", "type": "belongsTo", "target": "Recommendation::class"}], "constants": {"TYPE_LIKE": "like", "TYPE_DISLIKE": "dislike", "TYPE_SHARE": "share", "TYPE_SAVE": "save", "TYPE_CLICK": "click", "TYPE_VIEW": "view", "TYPE_SKIP": "skip", "STATUS_ACTIVE": "active", "STATUS_PROCESSED": "processed", "STATUS_IGNORED": "ignored", "CONTENT_TYPE_PROJECT": "project", "CONTENT_TYPE_RESOURCE": "resource", "CONTENT_TYPE_USER": "user", "CONTENT_TYPE_STYLE": "style"}, "casts": {"score": "decimal:2", "context_data": "array", "processed_at": "datetime", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/RecommendationFeedback.php"}, "p_resources": {"model_name": "Resource", "table_name": "p_resources", "model_table_name": "resources", "fillable_fields": ["resource_uuid", "user_id", "project_id", "resource_type", "status", "generation_config", "output_format", "quality_level", "batch_size", "estimated_cost", "actual_cost", "generation_task_id", "file_path", "file_size", "file_hash", "download_count", "processing_time_ms", "metadata", "completed_at"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "project", "type": "belongsTo", "target": "Project::class"}, {"method": "generationTasks", "type": "hasMany", "target": "AiGenerationTask::class, 'project_id', 'project_id'"}, {"method": "versions", "type": "hasMany", "target": "ResourceVersion::class"}, {"method": "currentVersion", "type": "belongsTo", "target": "ResourceVersion::class, 'current_version_id'"}, {"method": "exports", "type": "hasMany", "target": "ResourceExport::class"}, {"method": "downloads", "type": "hasMany", "target": "ResourceDownload::class"}], "constants": {"STATUS_PENDING": "pending", "STATUS_PROCESSING": "processing", "STATUS_COMPLETED": "completed", "STATUS_FAILED": "failed", "STATUS_CANCELLED": "cancelled", "STATUS_READY": "ready", "STATUS_AI_COMPLETED": "ai_completed", "TYPE_STORY": "story", "TYPE_IMAGE": "image", "TYPE_VOICE": "voice", "TYPE_VIDEO": "video", "TYPE_MUSIC": "music", "TYPE_SOUND": "sound", "QUALITY_LOW": "low", "QUALITY_MEDIUM": "medium", "QUALITY_HIGH": "high", "QUALITY_ULTRA": "ultra"}, "casts": {"generation_config": "array", "metadata": "array", "estimated_cost": "decimal:4", "actual_cost": "decimal:4", "completed_at": "datetime", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/Resource.php"}, "p_resource_downloads": {"model_name": "ResourceDownload", "table_name": "p_resource_downloads", "model_table_name": "resource_downloads", "fillable_fields": ["user_id", "download_type", "target_id", "target_name", "file_path", "file_size", "download_token", "status", "download_count", "retry_count", "batch_id", "user_agent", "ip_address", "error_message", "metadata", "downloaded_at", "expires_at", "cancelled_at"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "target", "type": "belongsTo", "target": "Resource::class, 'target_id'"}], "constants": {"STATUS_READY": "ready", "STATUS_COMPLETED": "completed", "STATUS_FAILED": "failed", "STATUS_EXPIRED": "expired", "STATUS_PREPARING": "preparing", "TYPE_RESOURCE": "resource", "TYPE_EXPORT": "export", "TYPE_VERSION": "version", "TYPE_BATCH": "batch"}, "casts": {"metadata": "array", "downloaded_at": "datetime", "expires_at": "datetime", "cancelled_at": "datetime", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/ResourceDownload.php"}, "p_resource_exports": {"model_name": "ResourceExport", "table_name": "p_resource_exports", "model_table_name": "resource_exports", "fillable_fields": ["user_id", "resource_ids", "export_format", "export_options", "include_metadata", "include_versions", "compression_level", "status", "progress", "resource_count", "processed_count", "estimated_size", "estimated_duration", "file_path", "file_size", "download_count", "processing_time_ms", "error_message", "metadata", "started_at", "completed_at", "cancelled_at", "expires_at"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "resources", "type": "belongsToMany", "target": "Resource::class, 'resource_export_items', 'export_id', 'resource_id'"}], "constants": {"STATUS_PENDING": "pending", "STATUS_PROCESSING": "processing", "STATUS_COMPLETED": "completed", "STATUS_FAILED": "failed", "STATUS_CANCELLED": "cancelled", "FORMAT_ZIP": "zip", "FORMAT_JSON": "json", "FORMAT_XML": "xml", "FORMAT_CSV": "csv", "FORMAT_PDF": "pdf", "COMPRESSION_NONE": "none", "COMPRESSION_LOW": "low", "COMPRESSION_MEDIUM": "medium", "COMPRESSION_HIGH": "high"}, "casts": {"resource_ids": "array", "export_options": "array", "metadata": "array", "include_metadata": "boolean", "include_versions": "boolean", "progress": "integer", "started_at": "datetime", "completed_at": "datetime", "cancelled_at": "datetime", "expires_at": "datetime", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/ResourceExport.php"}, "p_resource_versions": {"model_name": "ResourceVersion", "table_name": "p_resource_versions", "model_table_name": "resource_versions", "fillable_fields": ["resource_id", "user_id", "version_number", "version_name", "description", "status", "generation_config", "generation_task_id", "base_version_id", "file_path", "file_url", "file_size", "file_hash", "estimated_cost", "actual_cost", "processing_time_ms", "is_current", "metadata", "completed_at"], "relationships": [{"method": "resource", "type": "belongsTo", "target": "Resource::class"}, {"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "generationTask", "type": "belongsTo", "target": "AiGenerationTask::class, 'generation_task_id'"}, {"method": "baseVersion", "type": "belongsTo", "target": "ResourceVersion::class, 'base_version_id'"}, {"method": "derivedVersions", "type": "hasMany", "target": "ResourceVersion::class, 'base_version_id'"}], "constants": {"STATUS_PENDING": "pending", "STATUS_PROCESSING": "processing", "STATUS_COMPLETED": "completed", "STATUS_FAILED": "failed", "STATUS_CANCELLED": "cancelled"}, "casts": {"generation_config": "array", "metadata": "array", "estimated_cost": "decimal:4", "actual_cost": "decimal:4", "is_current": "boolean", "completed_at": "datetime", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/ResourceVersion.php"}, "p_reviews": {"model_name": "Review", "table_name": "p_reviews", "model_table_name": "reviews", "fillable_fields": ["publication_id", "user_id", "reviewer_id", "review_type", "status", "priority", "review_score", "review_message", "review_criteria", "additional_info", "metadata", "reviewed_at"], "relationships": [{"method": "publication", "type": "belongsTo", "target": "Publication::class"}, {"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "reviewer", "type": "belongsTo", "target": "User::class, 'reviewer_id'"}, {"method": "appeals", "type": "hasMany", "target": "ReviewAppeal::class"}], "constants": {"STATUS_PENDING": "pending", "STATUS_APPROVED": "approved", "STATUS_REJECTED": "rejected", "STATUS_APPEALING": "appealing", "TYPE_AUTO": "auto", "TYPE_MANUAL": "manual", "TYPE_PRIORITY": "priority"}, "casts": {"review_criteria": "array", "metadata": "array", "reviewed_at": "datetime", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/Review.php"}, "p_review_appeals": {"model_name": "ReviewAppeal", "table_name": "p_review_appeals", "model_table_name": "review_appeals", "fillable_fields": ["review_id", "user_id", "appeal_reason", "additional_evidence", "status", "handler_id", "handler_message", "metadata", "handled_at"], "relationships": [{"method": "review", "type": "belongsTo", "target": "Review::class"}, {"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "handler", "type": "belongsTo", "target": "User::class, 'handler_id'"}], "constants": {"STATUS_PENDING": "pending", "STATUS_APPROVED": "approved", "STATUS_REJECTED": "rejected", "STATUS_PROCESSING": "processing"}, "casts": {"metadata": "array", "handled_at": "datetime", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/ReviewAppeal.php"}, "p_style_library": {"model_name": "StyleLibrary", "table_name": "p_style_library", "model_table_name": "style_library", "fillable_fields": ["name", "description", "category", "style_config", "prompt_template", "thumbnail", "is_active", "is_premium", "sort_order", "usage_count", "rating", "tags", "created_by"], "relationships": [{"method": "creator", "type": "belongsTo", "target": "User::class, 'created_by'"}, {"method": "projects", "type": "hasMany", "target": "Project::class, 'style_id'"}], "constants": {"CATEGORY_GENERAL": "general", "CATEGORY_ROMANCE": "romance", "CATEGORY_ADVENTURE": "adventure", "CATEGORY_FANTASY": "fantasy", "CATEGORY_SCIFI": "scifi", "CATEGORY_HORROR": "horror", "CATEGORY_COMEDY": "comedy", "CATEGORY_DRAMA": "drama"}, "casts": {"style_config": "array", "is_active": "boolean", "is_premium": "boolean", "sort_order": "integer", "usage_count": "integer", "rating": "decimal:2", "tags": "array", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/StyleLibrary.php"}, "p_system_monitors": {"model_name": "SystemMonitor", "table_name": "p_system_monitors", "model_table_name": "system_monitors", "fillable_fields": ["metric_type", "metric_name", "metric_value", "metric_unit", "metric_details", "source", "environment", "status", "alert_message", "collected_at"], "relationships": [], "constants": {"TYPE_SYSTEM": "system", "TYPE_DATABASE": "database", "TYPE_CACHE": "cache", "TYPE_QUEUE": "queue", "TYPE_API": "api", "TYPE_WEBSOCKET": "websocket", "TYPE_AI_SERVICE": "ai_service", "TYPE_STORAGE": "storage", "STATUS_NORMAL": "normal", "STATUS_WARNING": "warning", "STATUS_CRITICAL": "critical", "STATUS_UNKNOWN": "unknown", "ENV_PRODUCTION": "production", "ENV_STAGING": "staging", "ENV_DEVELOPMENT": "development"}, "casts": {"metric_value": "decimal:4", "metric_details": "array", "collected_at": "datetime", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/SystemMonitor.php"}, "p_templates": {"model_name": "Template", "table_name": "p_templates", "model_table_name": "templates", "fillable_fields": ["user_id", "name", "description", "type", "category", "source_type", "source_id", "visibility", "status", "tags", "configuration", "usage_count", "rating", "review_count", "share_count", "like_count", "comment_count", "featured", "metadata"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}], "constants": {"STATUS_ACTIVE": "active", "STATUS_INACTIVE": "inactive", "STATUS_PENDING": "pending", "VISIBILITY_PRIVATE": "private", "VISIBILITY_PUBLIC": "public", "VISIBILITY_TEAM": "team", "TYPE_PROJECT": "project", "TYPE_RESOURCE": "resource", "TYPE_WORKFLOW": "workflow"}, "casts": {"tags": "array", "configuration": "array", "metadata": "array", "featured": "boolean", "usage_count": "integer", "share_count": "integer", "like_count": "integer", "comment_count": "integer", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/Template.php"}, "p_users": {"model_name": "User", "table_name": "p_users", "model_table_name": "users", "fillable_fields": ["username", "email", "password", "nickname", "avatar", "bio", "level", "experience", "follower_count", "following_count", "inviter_id", "remark", "status", "points", "frozen_points", "is_vip", "vip_expires_at", "last_login_ip", "last_login_at"], "relationships": [{"method": "getAuthPassword", "type": "hasMany", "target": "PointsTransaction::class"}, {"method": "pointsFreeze", "type": "hasMany", "target": "PointsFreeze::class"}, {"method": "preference", "type": "hasOne", "target": "UserPreference::class"}, {"method": "projects", "type": "hasMany", "target": "Project::class"}, {"method": "createdStyles", "type": "hasMany", "target": "StyleLibrary::class, 'created_by'"}, {"method": "resources", "type": "hasMany", "target": "Resource::class"}, {"method": "modelPreferences", "type": "hasMany", "target": "UserModelPreference::class"}, {"method": "platformUsageStatistics", "type": "hasMany", "target": "PlatformUsageStatistic::class"}], "constants": [], "casts": {"email_verified_at": "datetime", "level": "integer", "follower_count": "integer", "following_count": "integer", "points": "decimal:2", "frozen_points": "decimal:2", "is_vip": "boolean", "vip_expires_at": "datetime", "last_login_at": "datetime", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/User.php"}, "p_user_achievements": {"model_name": "UserAchievement", "table_name": "p_user_achievements", "model_table_name": "user_achievements", "fillable_fields": ["user_id", "achievement_id", "unlocked_at", "progress", "current_value", "target_value", "is_notified", "notified_at", "metadata"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "achievement", "type": "belongsTo", "target": "Achievement::class"}], "constants": [], "casts": {"user_id": "integer", "achievement_id": "integer", "unlocked_at": "datetime", "progress": "decimal:2", "current_value": "integer", "target_value": "integer", "is_notified": "boolean", "notified_at": "datetime", "metadata": "array", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/UserAchievement.php"}, "p_user_character_bindings": {"model_name": "UserCharacterBinding", "table_name": "p_user_character_bindings", "model_table_name": "user_character_bindings", "fillable_fields": ["user_id", "character_id", "binding_name", "binding_reason", "custom_description", "custom_config", "is_active", "is_favorite", "usage_count", "last_used_at", "user_rating", "user_feedback", "usage_stats"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "character", "type": "belongsTo", "target": "CharacterLibrary::class, 'character_id'"}], "constants": [], "casts": {"custom_config": "array", "is_active": "boolean", "is_favorite": "boolean", "usage_count": "integer", "last_used_at": "datetime", "user_rating": "decimal:2", "usage_stats": "array", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/UserCharacterBinding.php"}, "p_user_daily_tasks": {"model_name": "UserDailyTask", "table_name": "p_user_daily_tasks", "model_table_name": "user_daily_tasks", "fillable_fields": ["user_id", "daily_task_id", "completed", "completed_at", "completion_count", "progress", "target_value", "current_value", "reward_claimed", "reward_claimed_at", "metadata"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "dailyTask", "type": "belongsTo", "target": "DailyTask::class"}], "constants": [], "casts": {"user_id": "integer", "daily_task_id": "integer", "completed": "boolean", "completed_at": "datetime", "completion_count": "integer", "progress": "decimal:2", "target_value": "integer", "current_value": "integer", "reward_claimed": "boolean", "reward_claimed_at": "datetime", "metadata": "array", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/UserDailyTask.php"}, "p_user_files": {"model_name": "UserFile", "table_name": "p_user_files", "model_table_name": "user_files", "fillable_fields": ["user_id", "filename", "original_name", "file_path", "file_url", "file_type", "mime_type", "file_size", "file_hash", "storage_driver", "folder_path", "metadata", "thumbnails", "is_public", "is_temporary", "download_count", "last_accessed_at", "expires_at"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}], "constants": {"TYPE_IMAGE": "image", "TYPE_AUDIO": "audio", "TYPE_VIDEO": "video", "TYPE_DOCUMENT": "document", "TYPE_ARCHIVE": "archive", "TYPE_OTHER": "other", "DRIVER_LOCAL": "local", "DRIVER_S3": "s3", "DRIVER_OSS": "oss", "DRIVER_COS": "cos"}, "casts": {"metadata": "array", "thumbnails": "array", "is_public": "boolean", "is_temporary": "boolean", "file_size": "integer", "download_count": "integer", "last_accessed_at": "datetime", "expires_at": "datetime", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/UserFile.php"}, "p_user_levels": {"model_name": "UserLevel", "table_name": "p_user_levels", "model_table_name": "user_levels", "fillable_fields": ["level", "name", "description", "type", "required_experience", "icon", "color", "badge", "privileges", "rewards", "unlock_features", "is_active", "sort_order", "metadata"], "relationships": [{"method": "users", "type": "hasMany", "target": "User::class, 'level', 'level'"}], "constants": {"TYPE_NORMAL": "normal", "TYPE_VIP": "vip", "TYPE_SPECIAL": "special", "TYPE_MILESTONE": "milestone"}, "casts": {"level": "integer", "required_experience": "integer", "privileges": "array", "rewards": "array", "unlock_features": "array", "is_active": "boolean", "sort_order": "integer", "metadata": "array", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/UserLevel.php"}, "p_user_model_preferences": {"model_name": "UserModelPreference", "table_name": "p_user_model_preferences", "model_table_name": "user_model_preferences", "fillable_fields": ["user_id", "business_type", "preferred_platform", "platform_priorities", "selection_criteria", "auto_fallback", "cost_optimization", "custom_config", "usage_count", "last_used_at"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "usageStatistics", "type": "hasMany", "target": "PlatformUsageStatistic::class, 'user_id', 'user_id'"}], "constants": {"BUSINESS_TYPE_IMAGE": "image", "BUSINESS_TYPE_VIDEO": "video", "BUSINESS_TYPE_STORY": "story", "BUSINESS_TYPE_CHARACTER": "character", "BUSINESS_TYPE_STYLE": "style", "BUSINESS_TYPE_VOICE": "voice", "BUSINESS_TYPE_SOUND": "sound", "BUSINESS_TYPE_MUSIC": "music", "CRITERIA_PERFORMANCE": "performance", "CRITERIA_COST": "cost", "CRITERIA_QUALITY": "quality", "CRITERIA_RELIABILITY": "reliability", "CRITERIA_SPEED": "speed"}, "casts": {"platform_priorities": "array", "selection_criteria": "array", "custom_config": "array", "auto_fallback": "boolean", "cost_optimization": "boolean", "usage_count": "integer", "last_used_at": "datetime", "created_at": "datetime", "updated_at": "datetime", "deleted_at": "datetime"}, "file_path": "php/api/app/Models/UserModelPreference.php"}, "p_user_preferences": {"model_name": "UserPreference", "table_name": "p_user_preferences", "model_table_name": "user_preferences", "fillable_fields": ["user_id", "language", "timezone", "email_notifications", "push_notifications", "ai_preferences", "ui_preferences", "workflow_preferences", "default_ai_model", "auto_save_interval", "show_tutorials"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}], "constants": [], "casts": {"email_notifications": "boolean", "push_notifications": "boolean", "ai_preferences": "array", "ui_preferences": "array", "workflow_preferences": "array", "auto_save_interval": "integer", "show_tutorials": "boolean", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/UserPreference.php"}, "p_user_works": {"model_name": "UserWork", "table_name": "p_user_works", "model_table_name": "user_works", "fillable_fields": ["user_id", "work_title", "work_description", "work_file_path", "work_thumbnail", "publish_status", "work_metadata", "view_count", "like_count", "share_count", "published_at"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}], "constants": {"PUBLISH_STATUS_DRAFT": "draft", "PUBLISH_STATUS_PUBLISHED": "published", "PUBLISH_STATUS_PRIVATE": "private"}, "casts": {"work_metadata": "array", "view_count": "integer", "like_count": "integer", "share_count": "integer", "published_at": "datetime"}, "file_path": "php/api/app/Models/UserWork.php"}, "p_websocket_sessions": {"model_name": "WebSocketSession", "table_name": "p_websocket_sessions", "model_table_name": "websocket_sessions", "fillable_fields": ["session_id", "user_id", "client_type", "client_version", "connection_ip", "user_agent", "status", "connection_info", "subscribed_events", "connected_at", "last_ping_at", "disconnected_at", "message_count", "disconnect_reason"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}], "constants": {"STATUS_CONNECTED": "connected", "STATUS_DISCONNECTED": "disconnected", "STATUS_TIMEOUT": "timeout", "CLIENT_TYPE_PYTHON_TOOL": "python_tool", "CLIENT_TYPE_WEB_BROWSER": "web_browser", "CLIENT_TYPE_MOBILE_APP": "mobile_app", "CLIENT_TYPE_UNKNOWN": "unknown", "EVENT_AI_GENERATION_PROGRESS": "ai_generation_progress", "EVENT_AI_GENERATION_COMPLETED": "ai_generation_completed", "EVENT_AI_GENERATION_FAILED": "ai_generation_failed", "EVENT_POINTS_CHANGED": "points_changed"}, "casts": {"connection_info": "array", "subscribed_events": "array", "connected_at": "datetime", "last_ping_at": "datetime", "disconnected_at": "datetime", "message_count": "integer", "created_at": "datetime", "updated_at": "datetime"}, "file_path": "php/api/app/Models/WebSocketSession.php"}, "p_work_interactions": {"model_name": "WorkInteraction", "table_name": "p_work_interactions", "model_table_name": "work_interactions", "fillable_fields": ["work_id", "user_id", "interaction_type", "interaction_content", "interaction_metadata"], "relationships": [{"method": "work", "type": "belongsTo", "target": "WorkPlaza::class, 'work_id'"}, {"method": "user", "type": "belongsTo", "target": "User::class"}], "constants": {"INTERACTION_TYPE_LIKE": "like", "INTERACTION_TYPE_VIEW": "view", "INTERACTION_TYPE_SHARE": "share", "INTERACTION_TYPE_COMMENT": "comment"}, "casts": {"interaction_metadata": "array"}, "file_path": "php/api/app/Models/WorkInteraction.php"}, "p_work_plaza": {"model_name": "WorkPlaza", "table_name": "p_work_plaza", "model_table_name": "work_plaza", "fillable_fields": ["work_uuid", "user_id", "work_type", "file_name", "file_extension", "file_size", "mime_type", "file_path", "work_title", "work_description", "work_tags", "source_resource_id", "source_module_id", "status", "auto_review_result", "auto_review_reason", "manual_reviewer_id", "manual_reviewed_at", "manual_review_notes", "publish_status", "published_at", "view_count", "like_count", "share_count", "download_count", "comment_count", "recommendation_score", "is_featured", "report_count"], "relationships": [{"method": "user", "type": "belongsTo", "target": "User::class"}, {"method": "sourceResource", "type": "belongsTo", "target": "Resource::class, 'source_resource_id'"}, {"method": "manualReviewer", "type": "belongsTo", "target": "User::class, 'manual_reviewer_id'"}, {"method": "shares", "type": "hasMany", "target": "WorkShare::class, 'work_id'"}, {"method": "interactions", "type": "hasMany", "target": "WorkInteraction::class, 'work_id'"}], "constants": {"WORK_TYPE_CHARACTER": "character", "WORK_TYPE_STYLE": "style", "WORK_TYPE_BACKGROUND_MUSIC": "background_music", "WORK_TYPE_VOICE_TONE": "voice_tone", "WORK_TYPE_SCENE_SOUND": "scene_sound", "WORK_TYPE_COMPOSITE_VIDEO": "composite_video", "AUTO_REVIEW_APPROVED": "approved", "AUTO_REVIEW_REJECTED": "rejected", "PUBLISH_STATUS_DRAFT": "draft", "PUBLISH_STATUS_PUBLISHED": "published", "PUBLISH_STATUS_HIDDEN": "hidden", "PUBLISH_STATUS_DELETED": "deleted"}, "casts": {"work_tags": "array", "file_size": "integer", "status": "integer", "view_count": "integer", "like_count": "integer", "share_count": "integer", "download_count": "integer", "comment_count": "integer", "recommendation_score": "decimal:2", "is_featured": "boolean", "report_count": "integer", "manual_reviewed_at": "datetime", "published_at": "datetime"}, "file_path": "php/api/app/Models/WorkPlaza.php"}, "p_work_shares": {"model_name": "WorkShare", "table_name": "p_work_shares", "model_table_name": "work_shares", "fillable_fields": ["work_id", "share_token", "share_type", "share_password", "expires_at", "access_count", "max_access_count", "is_active"], "relationships": [{"method": "work", "type": "belongsTo", "target": "WorkPlaza::class, 'work_id'"}], "constants": {"SHARE_TYPE_PUBLIC": "public", "SHARE_TYPE_PRIVATE": "private", "SHARE_TYPE_PASSWORD": "password"}, "casts": {"access_count": "integer", "max_access_count": "integer", "is_active": "boolean", "expires_at": "datetime"}, "file_path": "php/api/app/Models/WorkShare.php"}}, "categories": {"用户管理": ["p_users"], "项目管理": ["p_projects"], "AI生成": ["p_ai_generation_tasks", "p_ai_model_configs"], "资源管理": ["p_resources", "p_resource_versions", "p_resource_downloads", "p_resource_exports"], "内容库": ["p_style_library", "p_character_library", "p_character_categories"], "积分系统": ["p_points_transactions", "p_points_freeze"], "社交功能": ["p_user_works", "p_work_plaza", "p_work_shares", "p_work_interactions", "p_follows", "p_likes", "p_comments"], "系统管理": ["p_websocket_sessions", "p_platform_performance_metrics", "p_platform_usage_statistics", "p_user_model_preferences"]}}