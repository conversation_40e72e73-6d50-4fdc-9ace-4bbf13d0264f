<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Py视频创作工具业务流程B-5: 数据处理流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.close()">← 返回总览</button>
    <div class="container">
        <h1>📊 Py视频创作工具业务流程B-5: 数据处理流程</h1>
        <div class="mermaid">
sequenceDiagram
    participant P as Py视频创作工具
    participant A as 工具API接口服务
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant FS as 文件存储

    P->>A: 上传项目数据(文件/参数/Token)
    A->>A: 验证Token和文件格式
    A->>A: 验证文件大小和类型
    alt 文件不符合要求
        A->>P: 返回上传失败(格式/大小错误)
    else 文件符合要求
        A->>FS: 存储文件到临时目录
        A->>A: 解析和验证数据内容
        A->>DB: 保存数据处理记录
        A->>R: 缓存处理状态
        A->>P: 返回上传成功(数据ID)
        
        Note over A: 异步处理数据
        A->>A: 数据清洗和格式化
        A->>DB: 更新处理进度
        A->>R: 更新缓存状态
        
        alt 数据处理成功
            A->>DB: 保存处理结果
            A->>FS: 移动文件到正式目录
            A->>P: 推送处理完成通知
        else 数据处理失败
            A->>DB: 记录错误信息
            A->>FS: 清理临时文件
            A->>P: 推送处理失败通知
        end
    end
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
